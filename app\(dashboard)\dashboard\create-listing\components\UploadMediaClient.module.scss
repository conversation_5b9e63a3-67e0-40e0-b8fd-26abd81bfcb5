@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @include only(largeDesktop) {
    width: rem(1098);
  }
  @include only(smallDesktop) {
    width: rem(1098);
  }
  @include only(tablet) {
    width: rem(1098);
  }

  .title {
    margin-top: rem(52);
    margin-bottom: rem(32);
    color: $danger;
    font-weight: 500;
    font-size: $h4;

    @include only(largeDesktop) {
      margin-top: rem(224);
      margin-bottom: rem(48);
    }
    @include only(smallDesktop) {
      margin-top: rem(224);
      margin-bottom: rem(48);
    }
    @include only(tablet) {
      margin-top: rem(224);
      margin-bottom: rem(48);
    }
  }

  .uploadedPhotos {
    padding: 0 rem(100);
    width: 114%;
  }

  .mediaSection {
    display: flex;
    flex-direction: column;
    margin-bottom: rem(48);
  }

  .buttons {
    margin-top: rem(48);
  }

  .photosButtonContainer {
    margin-bottom: rem(20);
  }
  .videosButtonContainer {
    margin-bottom: rem(20);
  }
  .videoUrlsContainer {
    margin-bottom: rem(20);
  }
  .attachmentsButtonContainer {
    margin-bottom: rem(20);
  }
}
