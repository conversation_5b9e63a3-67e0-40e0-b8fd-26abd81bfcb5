<!-- Generated by SpecStory -->

<!-- cursor Session 17b321f2-fadc-4705-8110-3a8e3d51960a (2025-08-30 10:42Z) -->

# Fixing ReferenceError in next.config.js (2025-08-30 10:42Z)

_**User (2025-08-30 13:24Z)**_

I ma getting this error when I try to run my code:

file:///C:/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/next.config.js:8
    ppr: incremental,
         ^

ReferenceError: incremental is not defined
    at file:///C:/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/next.config.js:8:10
    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)
    at async loadConfig (C:\Users\<USER>\Dropbox\Oly_03_before_deploy\oly\node_modules\next\dist\server\config.js:1019:36)
    at async Module.nextDev (C:\Users\<USER>\Dropbox\Oly_03_before_deploy\oly\node_modules\next\dist\cli\next-dev.js:190:14)

Node.js v22.16.0

---

_**Assistant (default)**_

I need to examine your `next.config.js` file to see what's causing the `incremental` reference error.


---

Tool use: **read_file**

Read file: next.config.js

---

I can see the issue! On line 8, you have `ppr: incremental,` but `incremental` is not defined as a variable. In Next.js, the PPR (Partial Prerendering) configuration should be a boolean value, not a variable name.

Here's the fix for your `next.config.js` file:

```js:next.config.js
/** @type {import('next').NextConfig} */

const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ["mongoose"],
    optimizePackageImports: ["@phosphor-icons/react"],
    serverActions: true,
    ppr: true, // Changed from 'incremental' to true
  },

  // ... existing code ...
};

export default nextConfig;
```

The issue is that `incremental` is not a defined variable in your configuration. The `ppr` (Partial Prerendering) option should be set to `true` to enable it, or `false` to disable it. 

If you want to enable PPR, use `ppr: true`. If you want to disable it, use `ppr: false` or simply remove that line entirely.

This should resolve the `ReferenceError: incremental is not defined` error you're encountering.

---

_**User (2025-08-30 13:24Z)**_

ode:internal/process/promises:394
    triggerUncaughtException(err, true /* fromPromise */);
    ^

[CanaryOnlyError: The experimental feature "experimental.ppr" can only be enabled when using the latest canary version of Next.js.]

---

_**Assistant (default)**_

The error indicates that the PPR (Partial Prerendering) experimental feature can only be used with the canary version of Next.js, but you're likely using a stable version.

Let me check your current Next.js version and then provide the solution:


---

Tool use: **read_file**

Read file: package.json

---

I can see you're using Next.js version `^15.4.0-canary.42`, which is indeed a canary version. However, the error suggests that the PPR feature might not be available in this specific canary build or there might be a compatibility issue.

The solution is to remove the PPR configuration from your `next.config.js` since it's causing issues. Here's the updated configuration:

```js:next.config.js
/** @type {import('next').NextConfig} */

const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ["mongoose"],
    optimizePackageImports: ["@phosphor-icons/react"],
    serverActions: true,
    // Removed ppr: true to avoid the canary-only error
  },

  devIndicators: {
    pprIsrStatus: true,
    buildActivity: true,
    buildActivityPosition: "bottom-right",
  },

  // ... existing code ...
};

export default nextConfig;
```

The key changes:
1. **Removed `ppr: true`** from the experimental configuration
2. **Kept `pprIsrStatus: true`** in devIndicators as this is still valid for monitoring PPR status even when PPR itself is disabled

This should resolve the canary-only error you're encountering. PPR is still an experimental feature and may not be stable in all canary builds, so it's safer to disable it for now until it becomes more stable or you upgrade to a version that fully supports it.

---

_**User (2025-08-30 13:24Z)**_

I want to use the canary version

---

_**Assistant (default)**_

I understand you want to use the canary version with PPR enabled. Since you're already using Next.js `^15.4.0-canary.42` and you've added `ppr: true` back to your config, let me help you ensure you have the latest canary version that fully supports PPR.

Let me check if there's a more recent canary version available and help you update:


---

Tool use: **read_file**

Read file: package.json

---

To use the latest canary version with full PPR support, you should update to the most recent canary build. Here's what you need to do:

1. **Update to the latest canary version** by running this command in your terminal:

```bash
npm install next@canary
```

Or if you're using pnpm:
```bash
pnpm install next@canary
```

2. **Your current `next.config.js` should work fine** with the latest canary version:

```js:next.config.js
/** @type {import('next').NextConfig} */

const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ["mongoose"],
    optimizePackageImports: ["@phosphor-icons/react"],
    serverActions: true,
    ppr: true, // This should work with the latest canary
  },

  devIndicators: {
    pprIsrStatus: true,
    buildActivity: true,
    buildActivityPosition: "bottom-right",
  },

  // ... rest of your config
};

export default nextConfig;
```

3. **After updating**, clear your Next.js cache:
```bash
rm -rf .next
# or on Windows:
# rmdir /s .next
```

4. **Then restart your development server**:
```bash
npm run dev
```

The error you encountered was likely because the specific canary build `15.4.0-canary.42` might not have had full PPR support. The latest canary builds should have better PPR support and stability.

If you still encounter issues after updating to the latest canary, you might need to check the [Next.js canary releases](https://github.com/vercel/next.js/releases) to see which specific canary version has the most stable PPR implementation.

---

