<!-- Generated by SpecStory -->

<!-- cursor Session 5f548ece-bc37-4cba-b61d-2bf9101ba609 (2025-07-24 08:54Z) -->

# Build responsive image gallery layout (2025-07-24 08:54Z)

_**User (2025-08-07 09:17Z)**_

I am trying to build a listing page image galley. The gallery will show up to the first five images at a time. The images will either be landscape or portrait or a combination of landscape and portrait. I have shown all the possible layouts I can think of depending on the combination of images uploaded by a user to display their ad. We can look at the gallery as if it is constructed inside two boxes, each with a grid of four boxes (cells), or if the first image is portrait three tall boxes that are divided into 2 boxes each. If there is only one image we can conceive of it as one box. The layout of the gallery is shown in the uploaded image. The accompanying spreadsheet shows the same thing. L stands for landscape and P stands for portrait. The first five rows align show the possible layout of the gallery and align to the layouts in the image. The last two row show the order of images uploaded by the user that should result in those layouts. I am am trying to avoid a case where a landscape image sits between 2 portrait images or between a landscape and portrait image. Another way of thinking about what I want is I don't want a portrait image that comes after a landscape image if that landscape image is not the first image. Many of these layouts show the total amount of images uploaded by the user others only the total amount that can be shown and allow the user to show more images with the Show All Images button. I am using Next.js, Typescript and Sass for my app. Create a nextjs, typscirpt code to achieve this. I have provided a mock data galleryImages.ts. Create the component and its styles here @components/ and use the components here @test/ 

---

_**Assistant (default)**_



---

