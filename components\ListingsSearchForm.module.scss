@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.searchContainer {
  display: flex;
  flex-direction: column;
  align-items: center;

  .search {
    border: none;

    .categories {
      margin-bottom: rem(20);
    }

    .breadcrumbs {
      margin-bottom: rem(4);
    }

    .errorMessage {
      color: $danger;
      margin-bottom: rem(4);
    }

    .searchTerm {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: rem(12);

      .searchTermBreadcrumbs {
        // margin-top: rem(32);
      }

      .searchTermInputContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: rem(20);

        .searchTermInput {
          // width: rem(954);
        }
      }
    }

    .location {
      display: flex;
      flex-direction: column;
      align-items: center;

      .locationBreadcrumbs {
      }

      .locationInputContainer {
        margin-bottom: rem(20);
        display: flex;
        flex-direction: column;
        align-items: center;

        .locationInput {
          width: rem(514);
        }

        .locationInput .suggestion {
          width: calc(rem(514) * 0.96);
        }
      }
    }
  }

  .searchButton {
    margin-bottom: rem(20);
  }
}