<!-- Generated by SpecStory -->

<!-- cursor Session 13442798-bec1-46bf-9b57-eada61714836 (2025-08-28 20:06Z) -->

# Error: Dynamic href `listings/[object Object]` found in <Link> while using (2025-08-28 20:06Z)

_**User (2025-08-28 20:06Z)**_

Error: Dynamic href `listings/[object Object]` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href
    at LinkComponent (http://localhost:3000/_next/static/chunks/node_modules_next_46d440e8._.js:4916:49)
    at Object.react_stack_bottom_frame (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:13014:24)
    at renderWithHooks (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:4064:24)
    at updateFunctionComponent (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:5428:21)
    at beginWork (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:6022:24)
    at runWithFiberInDEV (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:890:74)
    at performUnitOfWork (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:8236:97)
    at workLoopSync (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:8128:40)
    at renderRootSync (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:8111:13)
    at performWorkOnRoot (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:7870:56)
    at performSyncWorkOnRoot (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:8828:9)
    at flushSyncWorkAcrossRoots_impl (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:8747:316)
    at processRootScheduleInMicrotask (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:8767:106)
    at http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_react-dom_1f56dc06._.js:8839:158

---

_**Assistant (default)**_

I need to examine the `ListingsSearchForm.tsx` file to identify the issue with the dynamic href in the Link component. Let me read the file to understand the problem.


---

Tool use: **read_file**

Read file: app/(root)/listings/components/ListingsSearchForm.tsx

---

I can see the issue! The error is occurring because there's a `Link` component being used somewhere that's receiving an object instead of a string for its `href` prop. However, I don't see any `Link` components in this file. The error mentions `listings/[object Object]`, which suggests the issue is likely in a parent component or in the `Breadcrumbs` component.

Let me check the `Breadcrumbs` component since it's being used in this file and has `href` properties:


---

Tool use: **file_search**

<details>
            <summary>Searched codebase "Breadcrumbs" • **2** results</summary>
        
| File |
|------|
| `c:\Users\<USER>\Dropbox\Oly_03_before_deploy\oly\components\Breadcrumbs.tsx` |
| `c:\Users\<USER>\Dropbox\Oly_03_before_deploy\oly\components\Breadcrumbs.module.scss` |

</details>

---

Tool use: **read_file**

Read file: components/Breadcrumbs.tsx

---

Now I can see the issue! In the `ListingsSearchForm.tsx` file, the `Breadcrumbs` component is being passed `href: "#"` for all breadcrumb items, but the error suggests that somewhere an object is being passed instead of a string.

Looking at the `ListingsSearchForm.tsx` file again, I can see that all the `href` values are hardcoded as `"#"`, which should be strings. However, the error message `listings/[object Object]` suggests that somewhere a dynamic route is being constructed with an object.

Let me search for any other files that might be using `Link` components with dynamic routes related to listings:


---

Tool use: **grep_search**

<details>
            <summary>Grep search for "href.*listings" • **6** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/.specstory/history/2025-08-28_20-06Z-untitled.md` | L7 | `Error: Dynamic href `listings/[object Object]` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href` |
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/.specstory/history/2025-06-29_16-04Z-separate-client-and-server-components-plan.md` | L1817 | `-             \{/* <Link href=\{`/listings/$\{ad?.slug\}`\} key=\{index\}> */\}` |
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/components/FeaturedListings.tsx` | L37 | `<Link href="/listings" className=\{styles.buttons\}>` |
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/components/FeaturedListings.tsx` | L68 | `<Link href="/listings" className=\{styles.buttons\}>` |
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/components/FeaturedListings.tsx` | L103 | `<Link href="/listings" className=\{styles.buttons\}>` |
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/components/FeaturedCategoriesClient.tsx` | L27 | `<Link href=\{`/listings/?category=$\{title.toLocaleLowerCase()\}`\}>` |
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/components/ListingsCollage.tsx` | L123 | `href=\{`listings/$\{listing.slug\}`\}` |
| `file:///c%3A/Users/<USER>/Dropbox/Oly_03_before_deploy/oly/dist/static/sanity-BFZkiLdZ.js` | L6425 | `⟪ 310256 characters skipped ⟫ype:"object",fields:[\{title:"URL",name:"href",type:"url"\},\{title:"Open in new tab",name:"blank",type:"boolean"\}]\}]\}},\{type:"image",options:\{hotspot:!0,metadata:["blurhash","lqip","palette"]\},fields:[P(\{name:"alt",title:"Alt Text",type:"stri` |

</details>

---

Found it! The issue is in the `ListingsCollage.tsx` file. Let me examine that file:


---

Tool use: **read_file**

Read file: components/ListingsCollage.tsx

---

I found the issue! On line 122, there's a `Link` component with:

```tsx
href={`listings/${listing.slug}`}
```

The problem is that `listing.slug` is likely an object (probably a Sanity reference object) rather than a string. Let me check the structure of the listing object by looking at the destructuring on line 116:

```tsx
const { _id, slug, images, title, description, price, user } = listing;
```

The issue is that `slug` is being used directly, but it's probably a Sanity reference object. Looking at the `ListingCard` component usage, I can see that it's using `slug?.current` instead of just `slug`. This suggests that `slug` is an object with a `current` property.

The fix is to change line 122 from:
```tsx
href={`listings/${listing.slug}`}
```

to:
```tsx
href={`listings/${listing.slug?.current}`}
```

Here's the corrected code:

```tsx:components/ListingsCollage.tsx
// ... existing code ...
      <Link
        href={`listings/${listing.slug?.current}`}
        key={listing._id}
        className={styles.cardContainer}
      >
// ... existing code ...
```

The error occurs because Next.js expects the `href` prop in `Link` components to be a string, but when `listing.slug` is an object (like a Sanity reference), it gets converted to `[object Object]` in the string template, resulting in the URL `listings/[object Object]`.

By using `listing.slug?.current`, we access the actual string value of the slug, which is what should be used in the URL.

---

