<!-- Generated by SpecStory -->

<!-- cursor Session c0f367ed-acc9-453a-9118-60d7cf229013 (2025-08-24 13:25Z) -->

# Unhandled promise rejection error (2025-08-24 13:25Z)

_**User (2025-08-24 13:25Z)**_

Error: [object Event]
    at coerceError (http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js:1425:73)
    at onUnhandledRejection (http://localhost:3000/_next/static/chunks/node_modules_next_dist_445d8acf._.js:2156:50)

---

_**Assistant (default)**_

I need to examine the `TabsClient.tsx` file to understand the error you're encountering. Let me read the file to see what might be causing the "[object Event]" error.


---

Tool use: **read_file**

Read file: components/TabsClient.tsx

---

I can see the issue! The error "[object Event]" is likely occurring because of how the `onClickHandlers` are being called. Looking at line 147, there's a potential issue with the event handling in the `TabsDesktop` component.

The problem is in this section:

```147:147:components/TabsClient.tsx
onClickHandlers?.[index]?.(event);
```

The issue is that `onClickHandlers` is typed as an array of functions that can be `undefined`, but when you call `onClickHandlers?.[index]?.(event)`, if the function at that index is `undefined`, it might be causing the error.

Let me also check if there are any other potential issues. The error suggests that an Event object is being thrown as an error, which typically happens when:

1. An event handler throws an error
2. A promise rejection is not properly handled
3. An async function fails

Here's the fix for the `TabsClient.tsx` component:

```tsx:components/TabsClient.tsx
// ... existing code ...
              onClick={(event) => {
                handleClick(index);
                // Add proper error handling for the onClickHandlers
                try {
                  if (onClickHandlers?.[index]) {
                    onClickHandlers[index]!(event);
                  }
                } catch (error) {
                  console.error('Error in tab click handler:', error);
                }
              }}
// ... existing code ...
```

The main changes are:

1. **Added try-catch block**: This will prevent any errors from the `onClickHandlers` from bubbling up and causing the "[object Event]" error.

2. **Improved null checking**: Instead of using optional chaining with the function call, we first check if the handler exists, then call it with the non-null assertion operator (`!`).

3. **Error logging**: If there's an error, it will be logged to the console instead of causing an unhandled rejection.

This should resolve the "[object Event]" error you're seeing. The error was likely occurring because one of the `onClickHandlers` functions was throwing an error or returning a promise that was being rejected without proper error handling.

---

