@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  justify-content: center;
  .swipper {
    display: flex;
    flex-direction: column;
  
    .fileContainer {
      position: relative;
      height: auto;
      width: rem(248);
      scroll-snap-align: start;
      transition: transform 0.3s ease-in-out;
      margin-bottom: rem(24);
      border-radius: rem(40);
      // cursor: pointer;
      transition: transform 0.2s;
  
      touch-action: none; // Prevent conflicting touch events
      user-select: none; // Prevent text selection during drag
  
      &.dragging {
        opacity: 0.5;
        transform: scale(0.95);
        cursor: grabbing;
      }
  
      &.dragOver {
        border: 2px dashed $primary-hover;
      }
  
      &:hover {
        cursor: grab;
        border: 2px dashed $primary-hover;
        &:active {
          cursor: grabbing;
        }
      }
  
      .deleteButtonContainer {
        position: absolute;
        top: rem(16);
        right: rem(20);
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: $white-one;
        width: rem(28);
        height: rem(28);
        border-radius: 50%;
        pointer-events: none; // Make delete button non-interactive during drag
        pointer-events: auto !important; // Allow click interactions
        cursor: pointer; // Show proper cursor
  
        &:hover {
          background-color: $white-three;
        }
  
        // Prevent any drag behavior on the button itself
        * {
          pointer-events: none;
        }
        .deleteButton {
        }
      }
  
      .image {
        border-radius: rem(40);
      }
    }
  
    .navButtons {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: rem(16);
  
      margin-bottom: rem(16);
  
      .button {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
