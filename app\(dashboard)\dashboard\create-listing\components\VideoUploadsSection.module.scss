@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
  .uploadedVideosContainer::-webkit-scrollbar {
  width: rem(8);
}

.uploadedVideosContainer::-webkit-scrollbar-track {
  margin-block: rem(80);
  background: transparent;
}

.uploadedVideosContainer::-webkit-scrollbar-thumb {
  background-color: $warning-hover;
  min-height: rem(56);
  border-radius: rem(4);
  margin-right: rem(4);
}

.uploadedVideosContainer::-webkit-scrollbar-thumb:hover {
  background-color: $warning-hover;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center;


.container::-webkit-scrollbar-button {
  display: none;
  width: 0;
  height: 0;
}
  .uploadedVideosContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: rem(48);
    margin-bottom: rem(64);
    row-gap: rem(20);
    width: rem(975);
    height: rem(420);
    overflow-y: auto;
    margin: rem(50) rem(50);
    
    .uploadedVideo {
    }
  }

  .uploadedAttachmentsContainer {
    width: rem(920);
    margin-top: rem(48);
    margin-bottom: rem(64);
  }

  .buttonContainer {
    margin-bottom: rem(20);
  }
}
