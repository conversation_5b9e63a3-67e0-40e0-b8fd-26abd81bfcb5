<!-- Generated by SpecStory -->

<!-- cursor Session a60dae5d-ffc4-4243-8496-83199cfbe833 (2025-07-11 09:52Z) -->

# Fix TypeScript function return type error (2025-07-11 09:52Z)

_**User (2025-07-11 09:52Z)**_

For the code present, we get this error:
```
Type '(index: number) => void' is not assignable to type '(index: number) => Promise<void>'.
  Type 'void' is not assignable to type 'Promise<void>'.
```
How can I resolve this? If you propose a fix, please make it concise.

---

_**Assistant**_

The error occurs because the `DetailsList` and `SpecificationsList` components expect the props `handleDeleteItem` and `updateDetail` to be asynchronous functions returning a `Promise<void>`, but in your `DetailsClient.tsx` file, you are passing synchronous functions that return `void`.

**Concise Fix:**  
Update your `handleDeleteItem` and `updateDetail` functions in `DetailsClient.tsx` to be `async` and return a `Promise<void>`. You can do this by simply adding the `async` keyword and returning a resolved promise if the function is otherwise synchronous.

**Example:**
```tsx
const handleDeleteItem = async (index: number): Promise<void> => {
  const updatedDetails = details.filter((_, i) => i !== index);
  updateDetailsInForm(updatedDetails);

  if (editIndex === index) {
    setEditIndex(null);
  }

  await trigger("details.list");
};

const updateDetail = async (index: number, updatedValue: string): Promise<void> => {
  if (updatedValue.trim()) {
    const updatedDetails = [...details];
    updatedDetails[index] = {
      ...updatedDetails[index],
      value: updatedValue.trim(),
    };

    updateDetailsInForm(updatedDetails);

    const isValid = await trigger("details.list");
    if (isValid) {
      setEditIndex(null);
    }
  }
};
```
**Summary:**  
- Add `async` to both functions.
- Use `await` for any asynchronous calls inside them (like `trigger`).
- This will satisfy the expected prop types and resolve the error.

---

