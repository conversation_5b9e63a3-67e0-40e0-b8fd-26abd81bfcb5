@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  @include only(largeDesktop) {
    width: rem(1098);
  }
  @include only(smallDesktop) {
    width: rem(1098);
  }
  @include only(tablet) {
    width: rem(1098);
  }

  .pageTitle {
    margin-top: rem(52);
    margin-bottom: rem(32);
    color: $danger;
    font-weight: 500;
    font-size: $h4;
    text-align: center;
    @include only(largeDesktop) {
      font-weight: 500;
      margin-top: rem(96);
      margin-bottom: rem(72);
    }
    @include only(smallDesktop) {
      font-weight: 500;
      margin-top: rem(96);
      margin-bottom: rem(72);
    }
    @include only(tablet) {
      font-weight: 500;
      margin-top: rem(96);
      margin-bottom: rem(72);
    }
  }

  .titleContainer {
    display: flex;
    justify-self: center;
    margin-bottom: rem(20);

    .title {
      @include only(largeDesktop) {
        width: rem(580);
        margin-bottom: rem(16);
      }
      @include only(smallDesktop) {
        width: rem(580);
        margin-bottom: rem(16);
      }
      @include only(tablet) {
        width: rem(580);
        margin-bottom: rem(16);
      }
    }
  }
  .descriptionContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    // background-color: $grey-one;
    border-radius: rem(40);
     width: rem(640);
      height: rem(348);
    .description {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: rem(240);

      @include only(largeDesktop) {
        width: rem(640);
        border-radius: rem(40);
      }
      @include only(smallDesktop) {
        width: rem(640);
        border-radius: rem(40);
      }
      @include only(tablet) {
        width: rem(640);
        border-radius: rem(40);
      }
    }
  }

  .submitButtonContainer {
    text-align: center;
    margin-top: rem(16);
  }
}
