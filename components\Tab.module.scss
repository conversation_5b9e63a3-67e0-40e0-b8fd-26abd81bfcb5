@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.tab {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: rem(100);
  height: rem(56);
  border-radius: rem(40);
  margin-bottom: rem(20);
  background-color: $white-three;
  box-shadow: $shadow-one;
  cursor: pointer;
  font-size: rem(14);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: $white-two;
  }

  &:active {
    background-color: $white-one;
  }

  &.active {
    background-color: $white-one;

    &:hover {
      background-color: $white-two;
    }
  }

  .count {
    position: absolute;
    top: 50%;
    right: 10%;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: translate(50%, -50%);
    font-weight: 500;
    width: rem(32);
    height: rem(32);
    border-radius: 50%;
    background-color: white;
  }
}
