import styles from "./VehiclePrice.module.scss";
import Button from "./Buttons";
interface VehiclePriceProps {}

const VehiclePrice = ({}: VehiclePriceProps) => {
  return (
    <div className={styles.container}>
      <p className={styles.description}>Choose how you'd like to view prices</p>
      {/* 
      <p className={styles.temp}>Or</p>
      <p className={styles.description}>
       Change how you view prices:
      </p> */}

      <div className={styles.buttonsContainer}>
        <Button
          buttonChildren="Cash Price"
          className={styles.searchByPrice}
          buttonType="primary"
          buttonSize="large"
          name="Search Button"
          type="submit"
          ariaLabel="Search Button"
          autoFocus={false}
          disabled={false}
        />
        <Button
          buttonChildren="2-Year Monthly Price"
          className={styles.searchByPrice}
          buttonType="normal"
          buttonSize="large"
          name="Search Button"
          type="submit"
          ariaLabel="Search Button"
          autoFocus={false}
          disabled={false}
        />
        <Button
          buttonChildren="4-Year Monthly Price"
          className={styles.searchByPrice}
          buttonType="normal"
          buttonSize="large"
          name="Search Button"
          type="submit"
          ariaLabel="Search Button"
          autoFocus={false}
          disabled={false}
        />
        <Button
          buttonChildren="6-Year Monthly Price"
          className={styles.searchByPrice}
          buttonType="normal"
          buttonSize="large"
          name="Search Button"
          type="submit"
          ariaLabel="Search Button"
          autoFocus={false}
          disabled={false}
        />
      </div>
    </div>
  );
};

export default VehiclePrice;
