@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.details {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: rem(648);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
  &.open {
    opacity: 1;
    transform: translateY(0);
  }

  .detail {
    display: flex;
    align-items: center;
    gap: rem(16);
    margin-bottom: rem(20);
    width: rem(480);

    .detailButtons {
      display: flex;
      gap: rem(16);
      margin-left: auto;
      margin-right: rem(24);
      height: rem(30);

      .deleteButtonContainer,
      .editButtonContainer {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #dbe3eb;
        width: rem(28);
        height: rem(28);
        border-radius: 50%;
        cursor: pointer;
      }
    }

    .detailText {
      font-weight: normal;
      margin-left: rem(24);
    }
  }

  .editMode {
    position: relative;
    width: rem(514);
    margin-bottom: rem(16);

    .editDetail {
      width: rem(514);
      height: rem(40);
      text-align: start;
      color: $black-one;
    }

    .submitButton {
      position: absolute;
      top: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      width: rem(40);
      height: rem(40);
      border-radius: 50%;
      transform: translateY(-50%);
      right: rem(8);
      z-index: 10;
      background-color: $white-one;
      cursor: pointer;
      box-shadow: 10px 10px 20px 0px rgba(169, 196, 203, 0.5),
        5px 5px 10px 0px rgba(169, 196, 203, 0.25);

      .iconContainer {
        margin-top: rem(4);
      }
    }
  }
}
