@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50vw;
  height: 93vh;
  border-radius: rem(150) rem(150) rem(150) rem(150);
  background-color: $grey-one;
  // background-color: #c9e5e8;

  .logo {
    margin-top: rem(80);
    margin-bottom: rem(24);
  }
  .buttonsAndSearch {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: rem(48);

    .buttons {
      display: flex;
      flex-direction: column;
      margin-bottom: rem(64);

      .link {
      }

      .createAListingBtn {
        margin-bottom: rem(20);
        background-color: $danger-hover;
        color: $white-one;
      }

      .createAListingBtn:hover {
        background-color: #f83710;
      }

      .propertyTypeSelectContainer {
        margin-bottom: rem(20);
      }
      .forSaleToLetSelectContainer {
        margin-bottom: rem(20);
        .forSaleToLetSelect {
        }
      }

      .priceRangeSelectContainer {
      }
      .categoriesBtn:hover {
        background-color: $white-one;
      }
    }

    .searchFields {
      margin-bottom: rem(30);
      .searchTerm {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: rem(20);

        .searchTermInput {
        }
      }
      .searchLocation {
        display: flex;
        flex-direction: column;
        align-items: center;

        .searchLocationInput {
          // margin-bottom: rem(10);
        }
      }

      .errorMessage {
        color: $danger;
        margin-bottom: rem(8);
      }
    }
    .searchButton {
      .search {
        // background-color: $white-two;
        // box-shadow: none;
      }

      .search:hover {
        background-color: $white-one;
      }
    }
  }
}
