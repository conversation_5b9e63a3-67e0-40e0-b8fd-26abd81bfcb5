@use "@/utils/functions" as *;

// Run  npm run extract-variables

// Colours
$white-one: #ffffff;
$white-two: #f9fcfd;
$white-three: #f3f7fa;
$white-four: #edf2f7;
$white-five: #e9fcff;

$black-one: #000000;
$black-two: #12161f;
$black-three: #1c2230;
$black-four: #434b4d;

$blue-one: #c9e5e8;
$blue-one-complimentary: #e8ccc9;
$blue-one-complimentary-two: #f8dad7;

$grey-one: #e4e6e7;
$grey-two: #afb0b0;
$grey-three: #dfddd6;
$grey-four: #7e9094;

$primary: #ccf6ff;
$primary-hover: #14d6ff;
$primary-disabled: #ebfcff;

$normal: #f3f7fa;
$normal-hover: #ffffff;
$normal-disabled: #f3f7fa;

$success: #ccffed;
$success-hover: #14ffae;
$success-disabled: #e5fff6;

$warning: #ffeacc;
$warning-hover: #ff9d14;
$warning-disabled: #fff4e5;

$danger: #ff6b4c;
$danger-hover: #ff3c14;
$danger-disabled: #ffaa99;

$info: #cce4ff;
$info-hover: #1482ff;
$info-disabled: #e5f1ff;

$link: #10badc;

// Type

$body: rem(16);
$h0: rem(72);
$h1: rem(48);
$h2: rem(32);
$h3: rem(24);
$h4: rem(20);

// Breakpoints
$min-mobile: 20em; // 320px
$max-mobile: 47.9375em; // 767px

$min-tablet: 48em; // 768px
$max-tablet: 74.9375em; // 1199px

$min-small-desktop: 75em; // 1200px
$max-small-desktop: 89.9375em; // 1439px

$min-large-desktop: 90em; // 1440px
$max-large-desktop: 121.9375em; // 1951px

$min-extra-large-desktop: 122em; // 1952px
$max-extra-large-desktop: null; // No max for extra-large-desktop


// Shadows

$shadow-gradient: linear-gradient(145deg, #f0f0f0, #cacaca);
$shadow-one:
  0px 1px 3px 0px rgba(180, 191, 203, 0.2),
  0px 5px 5px 0px rgba(180, 191, 203, 0.17),
  0px 11px 7px 0px rgba(180, 191, 203, 0.1),
  0px 20px 8px 0px rgba(180, 191, 203, 0.03),
  0px 30px 9px 0px rgba(180, 191, 203, 0);
$shadow-two:
  -5px -5px 10px 0px #fff inset,
  5px 5px 10px 0px rgba(170, 186, 204, 0.5) inset,
  2px 2px 4px 0px rgba(120, 145, 171, 0.25) inset,
  -2px -2px 4px 0px rgba(255, 255, 255, 0.5) inset;
$shadow-three:
  -5px -5px 10px 0px #fff,
  5px 5px 10px 0px rgba(170, 186, 204, 0.5),
  2px 2px 4px 0px rgba(120, 145, 171, 0.25),
  -2px -2px 4px 0px rgba(255, 255, 255, 0.5);

$shadow-four:
  0px 1px 3px 0px rgba(207, 211, 211, 0.98),
  0px 5px 5px 0px rgba(207, 211, 211, 0.85),
  0px 11px 7px 0px rgba(207, 211, 211, 0.5),
  0px 20px 8px 0px rgba(207, 211, 211, 0.15),
  0px 30px 9px 0px rgba(207, 211, 211, 0.02);
$shadow-five:
  0px 11px 25px 0px rgba(207, 211, 211, 0.2),
  0px 45px 45px 0px rgba(207, 211, 211, 0.17),
  0px 101px 61px 0px rgba(207, 211, 211, 0.1),
  0px 180px 72px 0px rgba(207, 211, 211, 0.03),
  0px 282px 79px 0px rgba(207, 211, 211, 0);
$shadow-six:
  10px 10px 20px 0px rgba(169, 196, 203, 0.5),
  5px 5px 10px 0px rgba(169, 196, 203, 0.25);

$shadow-seven:
  -10px -10px 20px #fff,
  10px 10px 20px rgba(169, 196, 203, 0.5),
  5px 5px 10px rgba(169, 196, 203, 0.25),
  0px 420px 168px 0px rgba(0, 0, 0, 0.01),
  -5px -5px 10px rgba(255, 255, 255, 0.5);

$shadow-eight:
  0px 1px 3px 0px rgba(180, 191, 203, 0.98),
  0px 5px 5px 0px rgba(180, 191, 203, 0.85),
  0px 11px 7px 0px rgba(180, 191, 203, 0.5),
  0px 20px 8px 0px rgba(180, 191, 203, 0.15),
  0px 30px 9px 0px rgba(180, 191, 203, 0.02);

// Transitions
$transition-one: all 0.2s ease;
$transition-two: all 0.3s ease;
$transition-three: all 0.5s ease;
