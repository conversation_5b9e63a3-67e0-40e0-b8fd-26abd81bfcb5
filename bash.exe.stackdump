Stack trace:
Frame         Function      Args
0007FFFFA8F0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF97F0) msys-2.0.dll+0x2118E
0007FFFFA8F0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFABC8) msys-2.0.dll+0x69BA
0007FFFFA8F0  0002100469F2 (00021028DF99, 0007FFFFA7A8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFA8F0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFA8F0  00021006A545 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFABD0  00021006B9A5 (0007FFFFA900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB201A0000 ntdll.dll
7FFB1F770000 KERNEL32.DLL
7FFB1DBF0000 KERNELBASE.dll
7FFB1F4F0000 USER32.dll
7FFB1DBC0000 win32u.dll
000210040000 msys-2.0.dll
7FFB1F710000 GDI32.dll
7FFB1D630000 gdi32full.dll
7FFB1D750000 msvcp_win.dll
7FFB1D870000 ucrtbase.dll
7FFB1F440000 advapi32.dll
7FFB1F090000 msvcrt.dll
7FFB1F270000 sechost.dll
7FFB1F140000 RPCRT4.dll
7FFB1CEA0000 CRYPTBASE.DLL
7FFB1D7F0000 bcryptPrimitives.dll
7FFB1DFF0000 IMM32.DLL
