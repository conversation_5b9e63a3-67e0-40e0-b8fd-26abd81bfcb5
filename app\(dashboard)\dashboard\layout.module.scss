@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
// Note: globals.css should be imported in root layout, not in CSS modules
.html {
  scroll-behavior: smooth;
}
.html ::-webkit-scrollbar {
  width: rem(12);
  height: rem(12);
}
.html ::-webkit-scrollbar-track {
  background-color: transparent;
  // margin-block: rem(80);
}
.html ::-webkit-scrollbar-thumb {
  background-color: $warning-hover;
  height: rem(56);
  border-radius: rem(4);
}
.html ::-webkit-scrollbar-thumb:hover {
  background-color: $warning-hover;
}

.body {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  width: 100vw;
  background-color: $white-four;
  font-size: rem(16);
  line-height: 1.5;
  color: $black-four;
  leading-trim: both;
  text-edge: cap;
  font-feature-settings: "liga" off;
  font-style: normal;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  scroll-behavior: smooth;

  @include only(largeDesktop) {
    width: 100vw;
  }
  @include only(smallDesktop) {
    width: 100vw;
  }
  @include only(tablet) {
    width: 100vw;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  div {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  h1,
  h2,
  h3,
  h4,
  h5 {
    margin-top: 0;
  }
  h1 {
    font-size: $h1;
  }
  h2 {
    font-size: $h2;
  }
  h3 {
    font-size: $h3;
  }
  h4 {
    font-size: $h4;
    font-weight: normal;
  }

  p {
    margin-top: 0;
  }

  ul {
    list-style-type: none;
  }

  li {
    list-style-position: outside !important;
    padding-left: 0;
    margin-left: 0;
  }

  a,
  a:visited,
  a:active {
    outline: none;
    text-decoration: none;
    color: inherit;
  }

  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
  }
  img {
    max-width: 100%;
    height: auto;
  }
}
