@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 95vw;

  .main {
    display: flex;
    flex-direction: column;
    align-items: center;

    .listingsSearchForm {
      position: relative;
      margin-top: rem(80);
      margin-bottom: rem(-32);
    }

    .listingsContainer {
      display: flex;
      flex-direction: column;
      align-items: center;

      .tabsContainer {
        position: sticky;
        display: flex;
        flex-direction: column;
        align-items: center;
        top: rem(0);
        width: 100vw;
        min-height: rem(200);
        z-index: 20;
        background-color: $white-four;

        .resultsTextContainer {
          height: rem(80);
          .resultsText {
            font-size: $body;
            text-align: center;
            margin-top: rem(48);
            margin-bottom: rem(-48);
            span {
              font-weight: 500;
              text-transform: capitalize;
              font-style: oblique;
            }
          }
        }

        .filters {
          margin-top: auto;
          margin-bottom: rem(32);
        }

        .resultsToolbar {
          margin-bottom: rem(-48);
        }

        .options {
          position: relative;
          background-color: $white-four;
          display: flex;
          flex-direction: column;
          align-items: center;
          max-height: rem(400);
          overflow-y: auto;
          margin-top: rem(-7);
          padding-bottom: rem(16);
          gap: rem(16);

          .option {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: rem(56) ;
            border-radius: rem(40);
            background-color: $white-two;
            box-shadow: $shadow-one;
            cursor: pointer;

            .optionText {
              font-size: $body;
              color: $black-four;
              text-align: center;
              font-weight: 400;
            }
          }

          .option:hover {
            background-color: $white-three;
          }
        }

        .options::-webkit-scrollbar-track {
          border-radius: rem(40);
          margin-block: rem(30);
        }
        

        .hideOptions {
          display: none;
        }
      }

      .getSticky {
        position: -webkit-sticky;
        position: sticky;
        top: 8vh;
      }

      .listings {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        margin-top: rem(112);
      }

      .pagination {
        margin-bottom: rem(112);
      }
    }
  }
}
