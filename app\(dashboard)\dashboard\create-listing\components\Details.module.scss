@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: auto;
  overflow-x: hidden;
  width: rem(311);

  @include only(largeDesktop) {
    width: rem(1098);
  }
  @include only(smallDesktop) {
    width: rem(1098);
  }
  @include only(tablet) {
    width: rem(1098);
  }

  .title {
    margin-top: rem(240);
    margin-bottom: rem(48);
    color: $danger;
    font-size: $h4;
    font-weight: 500;
  }

  .form {
    display: flex;
    flex-direction: column;
    align-items: center;

 

    .formElements {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .errorMessage {
        color: $danger;
        margin-bottom: rem(8);
      }
      .conditionsContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: rem(20);
      }

      .addSpecificationsContainer {
        margin-bottom: rem(20);
      }

      .selectDetailContainer {
        margin-bottom: rem(20);
      }

      .chosenDetailContainer {
        padding: rem(16) rem(32);
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: $white-one;
        border-radius: rem(40) rem(40) rem(44) rem(44);
        width: rem(548);
        min-height: rem(232);
        margin-bottom: rem(48);

        .chosenDetailTitle {
          color: $danger;
          margin-top: rem(24);
          margin-bottom: rem(8);
          text-align: center;
        }

        .chosenDetailDescription {
          color: $grey-four;
          text-align: center;
          margin-bottom: rem(48);
        }

        .chosenDetail {
          margin-bottom: rem(20);

          .chosenDetailInput {
            background-color: $white-two;
            width: rem(471);
          }
        }
      }

      .customDetailContainer {
        margin-bottom: rem(20);
      }
    }
  }

  .submitButtonContainer {
  }

  .buttons {
    display: flex;
    flex-direction: column;
    margin-bottom: rem(124);

    .proceedButton {
      margin-bottom: rem(20);
    }
  }
}
