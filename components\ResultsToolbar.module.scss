@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.options {
  position: relative;
  background-color: $white-four;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: rem(400);
  overflow-y: auto;
  margin-top: rem(-7);
  padding-bottom: rem(16);
  gap: rem(16);

  .option {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: rem(56);
    border-radius: rem(40);
    background-color: $white-two;
    box-shadow: $shadow-one;
    cursor: pointer;

    .optionText {
      font-size: $body;
      color: $black-four;
      text-align: center;
      font-weight: 400;
    }
  }

  .option:hover {
    background-color: $white-three;
  }
}

.options::-webkit-scrollbar-track {
  border-radius: rem(40);
  margin-block: rem(30);
}

.hideOptions {
  display: none;
}
