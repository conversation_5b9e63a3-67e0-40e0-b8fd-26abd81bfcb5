"use client";
import NumberInput from "./NumberInput";
import styles from "./RangeSlider.module.scss";
import { Slider } from "radix-ui";

interface RangeSliderProps {
  placeholder: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const RangeSlider = ({ placeholder, onChange }: RangeSliderProps) => {
  return (
    <div className={styles.container}>
      <div className={styles.inputContainer}>
        <NumberInput
          className={styles.input}
          min={0}
          max={9999999999}
          step={100}
          debounceTime={500}
          id="value"
          value={0}
          inputSize="large"
          placeholder={placeholder}
          autoFocus={false}
          required={true}
          onChange={onChange}
        />
      </div>
      <Slider.Root
        className={styles.slider}
        defaultValue={[50]}
        max={100}
        step={1}
      >
        <Slider.Track className={styles.sliderTrack}>
          <Slider.Range className={styles.sliderRange} />
        </Slider.Track>
        <Slider.Thumb className={styles.sliderThumb} aria-label="Volume" />
      </Slider.Root>
    </div>
  );
};

export default RangeSlider;
