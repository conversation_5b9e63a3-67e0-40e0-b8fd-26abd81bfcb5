@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: rem(640);
  height: rem(240);
  border-radius: rem(50);
  background-color: $grey-one;

  .inputContainer {
    margin-bottom: rem(32);
    .input {
    }
  }
  .slider {
    position: relative;
    display: flex;
    align-items: center;
    user-select: none;
    touch-action: none;
    width: rem(480);
    height: 20px;
  }

  .sliderTrack {
    position: relative;
    flex-grow: 1;
    border-radius: 9999px;
    height: rem(12);
    background-color: $white-four;
  }

  .sliderRange {
    position: absolute;
    background-color: $primary-hover;
    border-radius: 9999px;
    height: 100%;
  }

  .sliderThumb {
    display: block;
    width: rem(32);
    height: rem(32);
    background-color: white;
    box-shadow: $shadow-one;
    border-radius: rem(32);

    cursor: pointer;
    &:focus {
      outline: none;
    
    }
  }
  
}
