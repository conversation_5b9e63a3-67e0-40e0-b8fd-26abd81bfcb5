<!-- Generated by SpecS<PERSON> -->

<!-- cursor Session 9a829779-3c84-4e7b-ab20-a69b26cf0311 (2025-07-24 09:01Z) -->

# Sanity cms studio css error (2025-07-24 09:01Z)

_**User (2025-07-24 08:53Z)**_

I installed the latest Sanity cms and the studio should be found in /studio. When I go to /studio I get this error:
Error: ./node_modules/@sanity/vision/lib/css/visionGui.css:113:16
Parsing css source code failed
[0m [90m 111 |[39m }
 [90m 112 |[39m
[31m[1m>[22m[39m[90m 113 |[39m [33m.[39medit [33m:[39mglobal([33m.[39m[33mPane[39m) {
 [90m     |[39m                [31m[1m^[22m[39m
 [90m 114 |[39m   overflow[33m:[39m hidden[33m;[39m
 [90m 115 |[39m }
 [90m 116 |[39m[0m

'global' is not recognized as a valid pseudo-class. Did you mean '::global' (pseudo-element) or is this a typo? at [project]/node_modules/@sanity/vision/lib/css/visionGui.css:112:15
    at BuildError (http://localhost:3000/_next/static/chunks/%5Broot-of-the-server%5D__16ba2972._.js:17359:41)
    at renderWithHooks (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:13260:28)
    at updateFunctionComponent (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:16755:32)
    at beginWork (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:18388:32)
    at beginWork$1 (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:23101:28)
    at performUnitOfWork (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:22402:24)
    at workLoopSync (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:22331:17)
    at renderRootSync (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:22308:21)
    at performConcurrentWorkOnRoot (http://localhost:3000/_next/static/chunks/node_modules_react-dom_cjs_react-dom_development_ab7e073c.js:21732:84)
    at workLoop (http://localhost:3000/_next/static/chunks/node_modules_3bfdc6a4._.js:291:48)
    at flushWork (http://localhost:3000/_next/static/chunks/node_modules_3bfdc6a4._.js:270:28)
    at MessagePort.performWorkUntilDeadline (http://localhost:3000/_next/static/chunks/node_modules_3bfdc6a4._.js:498:35)

---

_**Assistant (default)**_

This error is caused by a CSS syntax incompatibility between Sanity Studio’s CSS (specifically, the use of `:global(...)` in their CSS) and your current build tool or framework (likely Next.js with its default CSS loader). The error message:

> 'global' is not recognized as a valid pseudo-class. Did you mean '::global' (pseudo-element) or is this a typo?

means that the CSS parser your app uses does not recognize the `:global(...)` syntax, which is used by Sanity Studio (and is valid in PostCSS or certain CSS-in-JS setups, but not in plain CSS or with some Next.js configs).

### Why does this happen?

- Sanity Studio uses Vite or its own build system, which supports this CSS syntax.
- Next.js (especially with its default CSS loader) does not support `:global(...)` in plain CSS files, only in CSS Modules (and then as `:global { ... }` or `::global`).

### How to fix

#### 1. **Run Sanity Studio as a separate app**

The recommended way is to run Sanity Studio as a standalone app, not as a route inside your Next.js app. This avoids all build tool conflicts.

- Go to your `/studio` directory in the terminal.
- Run: `npm run dev` (or `yarn dev`).
- Access Sanity Studio at `http://localhost:3333` (or whatever port it shows).

#### 2. **If you must serve Studio from `/studio` in Next.js**

This is not recommended, but if you want to embed or proxy it, you need to:

- Use the official [@sanity/next](https://www.sanity.io/docs/sanity-studio-in-nextjs) integration, which handles the build differences.
- Or, set up a reverse proxy in Next.js to forward `/studio` requests to the separately running Sanity Studio server.

#### 3. **Do not import Sanity Studio as a Next.js page/component**

If you have something like this in your Next.js app:

```js
import Studio from '../studio/Studio'
export default Studio
```

or

```js
import 'studio/someSanityCss.css'
```

**Remove it.** Instead, run Studio separately.

---

### Summary

- The error is due to incompatible CSS syntax between Sanity Studio and Next.js.
- The best solution is to run Sanity Studio as a separate app, not as a Next.js route.
- If you need `/studio` in your main app, use the official Sanity/Next.js integration or a reverse proxy.

Would you like step-by-step instructions for setting up the recommended approach? If so, let me know if you want to run Studio separately or integrate it into your Next.js app!

---

_**User (2025-07-24 08:53Z)**_




---

_**Assistant (default)**_



---

