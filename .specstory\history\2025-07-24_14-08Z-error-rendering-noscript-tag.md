<!-- Generated by SpecStory -->

<!-- cursor Session 60d5c9d1-40ee-4549-af73-8dcfb6e4e146 (2025-07-24 14:08Z) -->

# Error rendering noscript tag (2025-07-24 14:08Z)

_**User (2025-07-24 14:08Z)**_

Error: Cannot render <noscript> outside the main document. Try moving it into the root <head> tag.
    at createConsoleError (http://localhost:3000/_next/static/chunks/79591_next_dist_ac79633a._.js:1484:71)
    at handleConsoleError (http://localhost:3000/_next/static/chunks/79591_next_dist_ac79633a._.js:2090:54)
    at console.error (http://localhost:3000/_next/static/chunks/79591_next_dist_ac79633a._.js:2243:57)
    at isHostHoistableType (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:10932:56)
    at createFiberFromTypeAndProps (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:2695:84)
    at createFiberFromElement (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:2751:16)
    at reconcileChildFibersImpl (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:3657:393)
    at http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:3714:39
    at reconcileChildren (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:5304:51)
    at updateFunctionComponent (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:5434:9)
    at beginWork (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:6022:24)
    at runWithFiberInDEV (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:890:74)
    at performUnitOfWork (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8236:97)
    at workLoopConcurrentByScheduler (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8232:58)
    at renderRootConcurrent (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8214:71)
    at performWorkOnRoot (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:7846:176)
    at performWorkOnRootViaSchedulerTask (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8820:9)
    at MessagePort.performWorkUntilDeadline (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_a9703d93._.js:2588:64)

---

_**Assistant**_

Tool use: **read_file**

Read file: app/studio/[[...tool]]/page.tsx

---

Tool use: **codebase_search**

<details>
                <summary>Searched codebase "How does NextStudio from next-sanity/studio render its content? Does it use <noscript>?" • **21** results</summary>
            
| File | Lines |
|------|-------|
| `app/studio/[[...tool]]/page.tsx` | L1-13 |
| `app/studio/[[...tool]]/page.tsx` | L15-19 |
| `app/studio/[[...tool]]/page.tsx` | L13-15 |
| `app/admin/[[...index]]/page.tsx` | L1-8 |
| `components/richTextEditor/Notes.tsx` | L1-41 |
| `sanity.config.ts` | L1-30 |
| `package.json` | L59-119 |
| `package` | L1-64 |
| `package.json` | L1-59 |
| `app/registry.tsx` | L1-27 |
| `sanity/lib/live.ts` | L1-13 |
| `components/richTextEditor/RichTextEditor.tsx` | L1-17 |
| `sanityTemp/lib/live.ts` | L1-13 |
| `app/(root)/layout.tsx` | L18-66 |
| `package` | L64-100 |
| `app/(dashboard)/dashboard/post-your-ad/components/PostYourAdClient.tsx` | L56-86 |
| `app/(dashboard)/dashboard/post-your-ad/components/DashboardClient.tsx` | L1-51 |
| `components/richTextEditor/RichTextEditor.tsx` | L86-101 |
| `sanityTemp/package.json` | L1-45 |
| `next.config.js` | L1-59 |
| `app/(oly-auto)/auto/layout.tsx` | L17-61 |

</details>

---

Tool use: **grep_search**

<details>
            <summary>Grep search for "<noscript>" • **0** files</summary>
        
No results found
</details>

---

Tool use: **codebase_search**

<details>
                <summary>Searched codebase "NextStudio next-sanity/studio <noscript> error or workaround with Next.js app directory" • **21** results</summary>
            
| File | Lines |
|------|-------|
| `app/studio/[[...tool]]/page.tsx` | L1-13 |
| `app/admin/[[...index]]/page.tsx` | L1-8 |
| `app/studio/[[...tool]]/page.tsx` | L15-19 |
| `app/studio/[[...tool]]/page.tsx` | L13-15 |
| `package` | L1-64 |
| `sanity.config.ts` | L1-30 |
| `package.json` | L1-59 |
| `next.config.js` | L1-59 |
| `package.json` | L59-119 |
| `tsconfig.json` | L1-57 |
| `features/messages/blog.md` | L153-203 |
| `sanityTemp/package.json` | L1-45 |
| `package` | L64-100 |
| `package.json` | L119-169 |
| `app/registry.tsx` | L1-27 |
| `app/(root)/layout.tsx` | L2-18 |
| `app/(root)/layout.tsx` | L18-66 |
| `sanity/lib/client.ts` | L1-10 |
| `sanityTemp/sanity.config.ts` | L1-26 |
| `features/messages/blog.md` | L203-275 |
| `sanityTemp/lib/client.ts` | L1-10 |

</details>

---

Tool use: **web_search**

<details>
            <summary>Searched web `Next.js NextStudio next-sanity <noscript> error workaround app directory` • **6** results</summary>
        
| Title | URL | Chunk |
|-------|-----|-------|
| GitHub - svirins/next-appdir-sanity | https://github.com/svirins/next-appdir-sanity | Skip to content <br/><br/>## Navigation Menu<br/><br/>Toggle navigation <br/><br/> Sign in <br/><br/>Appearance settings <br/><br/>* Product  <br/>   * GitHub Copilot Write better code with AI  <br/>   * GitHub Spark New  Build and deploy intelligent apps  <br/>   * GitHub Models New  Manage and compare prompts  <br/>   * GitHub Advanced Security Find and fix vulnerabilities  <br/>   * Actions Automate any workflow  <br/>   * Codespaces Instant dev environments  <br/>   * Issues Plan and track work  <br/>   * Code Review Manage code changes  <br/>   * Discussions Collaborate outside of code  <br/>   * Code Search Find more, search less  <br/>Explore  <br/>   * Why GitHub  <br/>   * All features  <br/>   * Documentation  <br/>   * GitHub Skills  <br/>   * Blog<br/>* Solutions  <br/>By company size  <br/>   * Enterprises  <br/>   * Small and medium teams  <br/>   * Startups  <br/>   * Nonprofits  <br/>By use case  <br/>   * DevSecOps  <br/>   * DevOps  <br/>   * CI/CD  <br/>   * View all use cases  <br/>By industry  <br/>   * Healthcare  <br/>   * Financial services  <br/>   * Manufacturing  <br/>   * Government  <br/>   * View all industries  <br/> View all solutions<br/>* Resources  <br/>Topics  <br/>   * AI  <br/>   * DevOps  <br/>   * Security  <br/>   * Software Development  <br/>   * View all  <br/>Explore  <br/>   * Learning Pathways  <br/>   * Events & Webinars  <br/>   * Ebooks & Whitepapers  <br/>   * Customer Stories  <br/>   * Partners  <br/>   * Executive Insights<br/>* Open Source  <br/>   * GitHub Sponsors Fund open source developers  <br/>   * The ReadME Project GitHub community articles  <br/>Repositories  <br/>   * Topics  <br/>   * Trending  <br/>   * Collections<br/>* Enterprise  <br/>   * Enterprise platform AI-powered developer platform  <br/>Available add-ons  <br/>   * GitHub Advanced Security Enterprise-grade security features  <br/>   * Copilot for business Enterprise-grade AI features  <br/>   * Premium Support Enterprise-grade 24/7 support<br/>* Pricing<br/><br/>Search or jump to... <br/><br/># Search code, repositories, users, issues, pull requests...<br/><br/> Search <br/><br/>Clear <br/><br/>Search syntax tips <br/><br/>#  Provide feedback<br/><br/>We read every piece of feedback, and take your input very seriously.<br/><br/>Include my email address so I can be contacted <br/><br/> Cancel  Submit feedback <br/><br/>#  Saved searches<br/><br/>## Use saved searches to filter your results more quickly<br/><br/>Name <br/><br/>Query <br/><br/> To see all available qualifiers, see our documentation.<br/><br/> Cancel  Create saved search <br/><br/> Sign in <br/><br/> Sign up <br/><br/>Appearance settings <br/><br/>Resetting focus <br/><br/>You signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert <br/><br/>\{{ message \}}<br/><br/> svirins / **next-appdir-sanity** Public <br/><br/> generated from hafffe/nextjs-sanity-template <br/><br/>* Notifications You must be signed in to change notification settings<br/>* Fork0<br/>* Star 2<br/><br/>2 stars 0 forks Branches Tags Activity <br/><br/> Star <br/><br/>Notifications You must be signed in to change notification settings <br/><br/>* Code<br/>* Issues 0<br/>* Pull requests 10<br/>* Actions<br/>* Projects 0<br/>* Security  <br/> Uh oh!  <br/>There was an error while loading. Please reload this page.<br/>* Insights<br/><br/>Additional navigation options <br/><br/>* Code<br/>* Issues<br/>* Pull requests<br/>* Actions<br/>* Projects<br/>* Security<br/>* Insights<br/><br/># svirins/next-appdir-sanity<br/><br/>main<br/><br/>BranchesTags<br/><br/>Go to file<br/><br/>Code<br/><br/>Open more actions menu<br/><br/>## Folders and files<br/><br/>\| Name                                                                                                     \| Name                                                                                                     \| Last commit message \| Last commit date \|<br/>\| -------------------------------------------------------------------------------------------------------- \| -------------------------------------------------------------------------------------------------------- \| ------------------- \| ---------------- \|<br/>\| Latest commit History[1 Commit](/svirins/next-appdir-sanity/commits/main/)                               \|                                                                                                          \|                     \|                  \|<br/>\| [.github](/svirins/next-appdir-sanity/tree/main/.github ".github")                                       \| [.github](/svirins/next-appdir-sanity/tree/main/.github ".github")                                       \|                     \|                  \|<br/>\| [.vscode](/svirins/next-appdir-sanity/tree/main/.vscode ".vscode")                                       \| [.vscode](/svirins/next-appdir-sanity/tree/main/.vscode ".vscode")                                       \|                     \|                  \|<br/>\| [app](/svirins/next-appdir-sanity/tree/main/app "app")                                                   \| [app](/svirins/next-appdir-sanity/tree/main/app "app")                                                   \|                     \|                  \|<br/>\| [components](/svirins/next-appdir-sanity/tree/main/components "components")                              \| [components](/svirins/next-appdir-sanity/tree/main/components "components")                              \|                     \|                  \|<br/>\| [hooks](/svirins/next-appdir-sanity/tree/main/hooks "hooks")                                             \| [hooks](/svirins/next-appdir-sanity/tree/main/hooks "hooks")                                             \|                     \|                  \|<br/>\| [lib](/svirins/next-appdir-sanity/tree/main/lib "lib")                                                   \| [lib](/svirins/next-appdir-sanity/tree/main/lib "lib")                                                   \|                     \|                  \|<br/>\| [models](/svirins/next-appdir-sanity/tree/main/models "models")                                          \| [models](/svirins/next-appdir-sanity/tree/main/models "models")                                          \|                     \|                  \|<br/>\| [pages/api](/svirins/next-appdir-sanity/tree/main/pages/api "This path skips through empty directories") \| [pages/api](/svirins/next-appdir-sanity/tree/main/pages/api "This path skips through empty directories") \|                     \|                  \|<br/>\| [public](/svirins/next-appdir-sanity/tree/main/public "public")                                          \| [public](/svirins/next-appdir-sanity/tree/main/public "public")                                          \|                     \|                  \|<br/>\| [studio](/svirins/next-appdir-sanity/tree/main/studio "studio")                                          \| [studio](/svirins/next-appdir-sanity/tree/main/studio "studio")                                          \|                     \|                  \|<br/>\| [styles](/svirins/next-appdir-sanity/tree/main/styles "styles")                                          \| [styles](/svirins/next-appdir-sanity/tree/main/styles "styles")                                          \|                     \|                  \|<br/>\| [.editorconfig](/svirins/next-appdir-sanity/blob/main/.editorconfig ".editorconfig")                     \| [.editorconfig](/svirins/next-appdir-sanity/blob/main/.editorconfig ".editorconfig")                     \|                     \|                  \|<br/>\| [.gitignore](/svirins/next-appdir-sanity/blob/main/.gitignore ".gitignore")                              \| [.gitignore](/svirins/next-appdir-sanity/blob/main/.gitignore ".gitignore")                              \|                     \|                  \|<br/>\| [globals.d.ts](/svirins/next-appdir-sanity/blob/main/globals.d.ts "globals.d.ts")                        \| [globals.d.ts](/svirins/next-appdir-sanity/blob/main/globals.d.ts "globals.d.ts")                        \|                     \|                  \|<br/>\| [next-env.d.ts](/svirins/next-appdir-sanity/blob/main/next-env.d.ts "next-env.d.ts")                     \| [next-env.d.ts](/svirins/next-appdir-sanity/blob/main/next-env.d.ts "next-env.d.ts")                     \|                     \|                  \|<br/>\| [next-seo.config.ts](/svirins/next-appdir-sanity/blob/main/next-seo.config.ts "next-seo.config.ts")      \| [next-seo.config.ts](/svirins/next-appdir-sanity/blob/main/next-seo.config.ts "next-seo.config.ts")      \|                     \|                  \|<br/>\| [next.config.mjs](/svirins/next-appdir-sanity/blob/main/next.config.mjs "next.config.mjs")               \| [next.config.mjs](/svirins/next-appdir-sanity/blob/main/next.config.mjs "next.config.mjs")               \|                     \|                  \|<br/>\| [package-lock.json](/svirins/next-appdir-sanity/blob/main/package-lock.json "package-lock.json")         \| [package-lock.json](/svirins/next-appdir-sanity/blob/main/package-lock.json "package-lock.json")         \|                     \|                  \|<br/>\| [package.json](/svirins/next-appdir-sanity/blob/main/package.json "package.json")                        \| [package.json](/svirins/next-appdir-sanity/blob/main/package.json "package.json")                        \|                     \|                  \|<br/>\| [postcss.config.js](/svirins/next-appdir-sanity/blob/main/postcss.config.js "postcss.config.js")         \| [postcss.config.js](/svirins/next-appdir-sanity/blob/main/postcss.config.js "postcss.config.js")         \|                     \|                  \|<br/>\| [readme.md](/svirins/next-appdir-sanity/blob/main/readme.md "readme.md")                                 \| [readme.md](/svirins/next-appdir-sanity/blob/main/readme.md "readme.md")                                 \|                     \|                  \|<br/>\| [sanity.config.ts](/svirins/next-appdir-sanity/blob/main/sanity.config.ts "sanity.config.ts")            \| [sanity.config.ts](/svirins/next-appdir-sanity/blob/main/sanity.config.ts "sanity.config.ts")            \|                     \|                  \|<br/>\| [tailwind.config.js](/svirins/next-appdir-sanity/blob/main/tailwind.config.js "tailwind.config.js")      \| [tailwind.config.js](/svirins/next-appdir-sanity/blob/main/tailwind.config.js "tailwind.config.js")      \|                     \|                  \|<br/>\| [tsconfig.json](/svirins/next-appdir-sanity/blob/main/tsconfig.json "tsconfig.json")                     \| [tsconfig.json](/svirins/next-appdir-sanity/blob/main/tsconfig.json "tsconfig.json")                     \|                     \|                  \|<br/>\| View all files                                                                                           \|                                                                                                          \|                     \|                  \|<br/><br/>## Repository files navigation<br/><br/>* README<br/><br/># NEXT.JS - SANITY TEMPLATE<br/><br/>> Template for blog / marketing site built next.js & sanity.io<br/><br/>This starter template built upon Next.js 13 with the new app directory & router.Sanity is used for content handling, and Tailwind is used for styling.<br/><br/>### Setup Vercel<br/><br/>Import the project in vercel Setup following environment variables (as plain text variables)<br/><br/>`NEXT_PUBLIC_SANITY_PROJECT_ID` with value for your prefered dataset`NEXT_PUBLIC_SANITY_DATASET` with your sanity project id<br/><br/>### Setup for local development<br/><br/>Create `.env` file in root path with the environment variables<br/><br/>```<br/># For Next Locally<br/>NEXT_PUBLIC_SANITY_PROJECT_ID = "abcd1245"<br/>NEXT_PUBLIC_SANITY_DATASET = "production"<br/><br/>```<br/><br/>### Development<br/><br/>To Start development server:<br/><br/>```<br/>npm run dev<br/><br/>```<br/><br/>This will run frontend at localhost:3000 and you will find sanity studio at localhost:3000/studio<br/><br/>Add <http://localhost:3000> in sanity settings -> api - CORS Origins<br/><br/>### Things to know<br/><br/>Frontend startpage is fetched with slug `frontpage`<br/><br/>### Tech Stack<br/><br/>* Next.js<br/>* Sanity<br/>* next-sanity<br/>* Tailwind<br/><br/>## About<br/><br/> No description, website, or topics provided.<br/><br/>### Resources<br/><br/> Readme <br/><br/>###  Uh oh!<br/><br/>There was an error while loading. Please reload this page.<br/><br/>Activity <br/><br/>### Stars<br/><br/>**2** stars <br/><br/>### Watchers<br/><br/>**1** watching <br/><br/>### Forks<br/><br/>**0** forks <br/><br/> Report repository <br/><br/>## Releases<br/><br/>No releases published<br/><br/>## Packages0<br/><br/> No packages published   <br/><br/>###  Uh oh!<br/><br/>There was an error while loading. Please reload this page.<br/><br/>## Languages<br/><br/>* TypeScript 98.6%<br/>* JavaScript 1.2%<br/>* CSS 0.2%<br/><br/>## Footer<br/><br/> © 2025 GitHub, Inc. <br/><br/>### Footer navigation<br/><br/>* Terms<br/>* Privacy<br/>* Security<br/>* Status<br/>* Docs<br/>* Contact<br/>* Manage cookies<br/>* Do not share my personal information<br/><br/> You can’t perform that action at this time. |
| How to embed Sanity (v3) Studio inside Next.js project (app dir) | https://medium.com/@aviralj02/how-to-embed-sanity-v3-studio-inside-next-js-13-project-527a2b509dee | Sitemap<br/><br/>Open in app<br/><br/>Sign up<br/><br/>Sign in<br/><br/>Medium Logo<br/><br/>Write<br/><br/>Sign up<br/><br/>Sign in<br/><br/># How to embed Sanity (v3) Studio inside Next.js project (app dir)<br/><br/>Aviral Jain<br/><br/>Aviral Jain<br/><br/>5 min read<br/><br/>·<br/><br/>Aug 7, 2023<br/><br/>\--<br/><br/>2<br/><br/>Listen<br/><br/>Share<br/><br/>Zoom image will be displayed<br/><br/>Photo by Sanity.io<br/><br/># Setting up Next.js Project<br/><br/>* Start with creating a Next.js Project with the following command.<br/><br/>$ npx create-next-app@latest<br/><br/>* In this tutorial, we’ll create a small Food Rating App using Typescript that retrieves all relevant data from a Sanity backend.<br/>* Execute the following command in the project directory to generate a Sanity project within the existing Next.js project. This will automatically create all the necessary configuration files needed for Sanity integration in the Next.js project.<br/><br/>$ npm -y create sanity@latest<br/><br/>* Upon running the command, you might be prompted to log in to your Sanity account. Simply log in and proceed to create a new project. Respond with a ‘Yes’ to all subsequent prompts that appear during the process.<br/><br/>Zoom image will be displayed<br/><br/>sanity project configuration<br/><br/>* The default `base path` to access the studio is `/studio`. You can modify it as per your preference, and you may change it later in the `sanity.config.ts` file.<br/>* After successful creation, you will see `/studio` path is now added to the app directory.<br/>* Now, the project will be visible in your Sanity account as well on Sanity’s website. Make sure to crosscheck the `project_id` present in your `.env` file with the one in your Sanity account for verification.<br/><br/>## Separating Layouts for client and studio pages<br/><br/>* Since the `/studio` path is within the app directory, it will be affected by the layouts defined in `layout.tsx`. This means the Navbar, which is intended for various pages of our Food App, will also be displayed in our Sanity studio.<br/>* To avoid layout conflicts between our client pages and the studio, we will organize these routes into different folders. Additionally, we will create distinct layouts for each, ensuring a clear separation between the two.<br/><br/>Zoom image will be displayed<br/><br/>Project Structure after segregating layouts<br/><br/>* Enclosing `(admin)` and `(client)` with parenthesis indicates the folder is for organizational purposes and should not be included in the route’s URL path.<br/>* Now all the client code can reside inside the `(client)` folder.<br/><br/># Setting up the schemas<br/><br/>* Create a folder `schemas` inside the `sanity` directory.<br/>* For this tutorial, I’m just going to make one single type which is `Food Items`.<br/>* Create a file `foodItems.ts` inside `schemas` folder and define fields.<br/><br/>Zoom image will be displayed<br/><br/>Github Gist Link<br/><br/>* The above code defines four fields for our food items.<br/>* Next, navigate to the `schema.ts` file within the `sanity` folder and import the schemas you have created. In my case, `foodItems.ts` will be imported.<br/><br/>Zoom image will be displayed<br/><br/>* Add your imported schema types inside the `types` array.<br/>* Run your app with following command:<br/><br/>$ npm run dev<br/><br/>* Access the `/studio` path (base path) in the URL.<br/>* To access this route, make sure you are logged in to your Sanity account.<br/>* If you’re accessing this route first time, it will ask you to add CORS origin to your sanity project.<br/>* Once you have added the CORS origin, your Sanity studio will be up and running, and the Food Items type will be created and accessible.<br/><br/>Zoom image will be displayed<br/><br/>Sanity Content Studio<br/><br/>* You can now add documents to your types directly in the studio. As a sample, I have added a few food items.<br/><br/>Zoom image will be displayed<br/><br/># Fetching Data from Studio<br/><br/>* Since we are using TypeScript, the first step is to define types for the data that we will be receiving from Sanity.<br/>* In the project’s root directory, create a file named `typings.d.ts` to contain all the necessary data types for the data we will fetch.<br/><br/>> NOTE: You can run GROQ queries on sanity studio under vision tab to check what type of data is returning in order to create data types.<br/><br/>Zoom image will be displayed<br/><br/>* Next, we’ll execute a GROQ query on our home page to extract the data for our `Food Items`. Import `groq` from `next-sanity` to run queries as shown below.<br/>* The query will execute and will return an array of all the food items added in the studio.<br/><br/>Zoom image will be displayed<br/><br/>> Since I have only one type of data to fetch, I’m performing the GROQ query directly on our home page. However, if you have multiple types of data to fetch, it’s a good practice to create utility functions to handle the queries and then import these functions into pages as needed. This approach helps keep the code organized and maintainable.<br/><br/>* I have created a `<RateCard />` component which receives each food item as a prop and will render a card for it.<br/>* The line `export const revalidate = 60` represents Incremental Static Regeneration (ISR), which refreshes the content on the app every 60 seconds whenever changes are made in the studio. This feature ensures that the app stays up-to-date with the latest content in a production environment.<br/><br/>> NOTE: When retrieving images from Sanity, you cannot directly use the image that is returned in the response. Instead, use the `urlForImage` function, which is automatically created when you install Sanity inside `sanity/lib/image.ts`. It helps you fetch the actual image URL and handle the image rendering appropriately. An example is given below:<br/><br/>Zoom image will be displayed<br/><br/>* Save your files and you are all set!<br/>* The result below shows that our Food Rating App is now successfully utilizing content from Sanity<br/><br/>Zoom image will be displayed<br/><br/>Food Rating App<br/><br/>* Our application is now ready, featuring Next.js with embedded Sanity CMS.<br/><br/>CMS<br/><br/>Nextjs<br/><br/>Typescript<br/><br/>Sanity<br/><br/>Isr<br/><br/>\--<br/><br/>\--<br/><br/>2<br/><br/>Aviral Jain<br/><br/>Aviral Jain<br/><br/>Written by Aviral Jain<br/><br/>15 followers<br/><br/>·11 following<br/><br/>Hi, I'm Aviral , a computer science engineering student and full-stack developer. My expertise lies in Java, React, JavaScript, TypeScript, Spring and more.<br/><br/>## Responses (2)<br/><br/>See all responses<br/><br/>Help<br/><br/>Status<br/><br/>About<br/><br/>Careers<br/><br/>Press<br/><br/>Blog<br/><br/>Privacy<br/><br/>Rules<br/><br/>Terms<br/><br/>Text to speech |
| GitHub - valerysid/nextjs_app | https://github.com/valerysid/nextjs_app | Skip to content <br/><br/>## Navigation Menu<br/><br/>Toggle navigation <br/><br/> Sign in <br/><br/>Appearance settings <br/><br/>* Product  <br/>   * GitHub Copilot Write better code with AI  <br/>   * GitHub Spark New  Build and deploy intelligent apps  <br/>   * GitHub Models New  Manage and compare prompts  <br/>   * GitHub Advanced Security Find and fix vulnerabilities  <br/>   * Actions Automate any workflow  <br/>   * Codespaces Instant dev environments  <br/>   * Issues Plan and track work  <br/>   * Code Review Manage code changes  <br/>   * Discussions Collaborate outside of code  <br/>   * Code Search Find more, search less  <br/>Explore  <br/>   * Why GitHub  <br/>   * All features  <br/>   * Documentation  <br/>   * GitHub Skills  <br/>   * Blog<br/>* Solutions  <br/>By company size  <br/>   * Enterprises  <br/>   * Small and medium teams  <br/>   * Startups  <br/>   * Nonprofits  <br/>By use case  <br/>   * DevSecOps  <br/>   * DevOps  <br/>   * CI/CD  <br/>   * View all use cases  <br/>By industry  <br/>   * Healthcare  <br/>   * Financial services  <br/>   * Manufacturing  <br/>   * Government  <br/>   * View all industries  <br/> View all solutions<br/>* Resources  <br/>Topics  <br/>   * AI  <br/>   * DevOps  <br/>   * Security  <br/>   * Software Development  <br/>   * View all  <br/>Explore  <br/>   * Learning Pathways  <br/>   * Events & Webinars  <br/>   * Ebooks & Whitepapers  <br/>   * Customer Stories  <br/>   * Partners  <br/>   * Executive Insights<br/>* Open Source  <br/>   * GitHub Sponsors Fund open source developers  <br/>   * The ReadME Project GitHub community articles  <br/>Repositories  <br/>   * Topics  <br/>   * Trending  <br/>   * Collections<br/>* Enterprise  <br/>   * Enterprise platform AI-powered developer platform  <br/>Available add-ons  <br/>   * GitHub Advanced Security Enterprise-grade security features  <br/>   * Copilot for business Enterprise-grade AI features  <br/>   * Premium Support Enterprise-grade 24/7 support<br/>* Pricing<br/><br/>Search or jump to... <br/><br/># Search code, repositories, users, issues, pull requests...<br/><br/> Search <br/><br/>Clear <br/><br/>Search syntax tips <br/><br/>#  Provide feedback<br/><br/>We read every piece of feedback, and take your input very seriously.<br/><br/>Include my email address so I can be contacted <br/><br/> Cancel  Submit feedback <br/><br/>#  Saved searches<br/><br/>## Use saved searches to filter your results more quickly<br/><br/>Name <br/><br/>Query <br/><br/> To see all available qualifiers, see our documentation.<br/><br/> Cancel  Create saved search <br/><br/> Sign in <br/><br/> Sign up <br/><br/>Appearance settings <br/><br/>Resetting focus <br/><br/>You signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert <br/><br/>\{{ message \}}<br/><br/> valerysid / **nextjs\_app** Public <br/><br/>* Notifications You must be signed in to change notification settings<br/>* Fork0<br/>* Star 1<br/><br/>1 star 0 forks Branches Tags Activity <br/><br/> Star <br/><br/>Notifications You must be signed in to change notification settings <br/><br/>* Code<br/>* Issues 0<br/>* Pull requests 0<br/>* Actions<br/>* Projects 0<br/>* Security  <br/> Uh oh!  <br/>There was an error while loading. Please reload this page.<br/>* Insights<br/><br/>Additional navigation options <br/><br/>* Code<br/>* Issues<br/>* Pull requests<br/>* Actions<br/>* Projects<br/>* Security<br/>* Insights<br/><br/># valerysid/nextjs\_app<br/><br/>main<br/><br/>BranchesTags<br/><br/>Go to file<br/><br/>Code<br/><br/>Open more actions menu<br/><br/>## Folders and files<br/><br/>\| Name                                                                                            \| Name                                                                                            \| Last commit message \| Last commit date \|<br/>\| ----------------------------------------------------------------------------------------------- \| ----------------------------------------------------------------------------------------------- \| ------------------- \| ---------------- \|<br/>\| Latest commit History[3 Commits](/valerysid/nextjs%5Fapp/commits/main/)                         \|                                                                                                 \|                     \|                  \|<br/>\| [components](/valerysid/nextjs%5Fapp/tree/main/components "components")                         \| [components](/valerysid/nextjs%5Fapp/tree/main/components "components")                         \|                     \|                  \|<br/>\| [context](/valerysid/nextjs%5Fapp/tree/main/context "context")                                  \| [context](/valerysid/nextjs%5Fapp/tree/main/context "context")                                  \|                     \|                  \|<br/>\| [firebase](/valerysid/nextjs%5Fapp/tree/main/firebase "firebase")                               \| [firebase](/valerysid/nextjs%5Fapp/tree/main/firebase "firebase")                               \|                     \|                  \|<br/>\| [hooks](/valerysid/nextjs%5Fapp/tree/main/hooks "hooks")                                        \| [hooks](/valerysid/nextjs%5Fapp/tree/main/hooks "hooks")                                        \|                     \|                  \|<br/>\| [pages](/valerysid/nextjs%5Fapp/tree/main/pages "pages")                                        \| [pages](/valerysid/nextjs%5Fapp/tree/main/pages "pages")                                        \|                     \|                  \|<br/>\| [public](/valerysid/nextjs%5Fapp/tree/main/public "public")                                     \| [public](/valerysid/nextjs%5Fapp/tree/main/public "public")                                     \|                     \|                  \|<br/>\| [studio](/valerysid/nextjs%5Fapp/tree/main/studio "studio")                                     \| [studio](/valerysid/nextjs%5Fapp/tree/main/studio "studio")                                     \|                     \|                  \|<br/>\| [utils](/valerysid/nextjs%5Fapp/tree/main/utils "utils")                                        \| [utils](/valerysid/nextjs%5Fapp/tree/main/utils "utils")                                        \|                     \|                  \|<br/>\| [.env.local](/valerysid/nextjs%5Fapp/blob/main/.env.local ".env.local")                         \| [.env.local](/valerysid/nextjs%5Fapp/blob/main/.env.local ".env.local")                         \|                     \|                  \|<br/>\| [.eslintrc.json](/valerysid/nextjs%5Fapp/blob/main/.eslintrc.json ".eslintrc.json")             \| [.eslintrc.json](/valerysid/nextjs%5Fapp/blob/main/.eslintrc.json ".eslintrc.json")             \|                     \|                  \|<br/>\| [.gitignore](/valerysid/nextjs%5Fapp/blob/main/.gitignore ".gitignore")                         \| [.gitignore](/valerysid/nextjs%5Fapp/blob/main/.gitignore ".gitignore")                         \|                     \|                  \|<br/>\| [README.md](/valerysid/nextjs%5Fapp/blob/main/README.md "README.md")                            \| [README.md](/valerysid/nextjs%5Fapp/blob/main/README.md "README.md")                            \|                     \|                  \|<br/>\| [next.config.js](/valerysid/nextjs%5Fapp/blob/main/next.config.js "next.config.js")             \| [next.config.js](/valerysid/nextjs%5Fapp/blob/main/next.config.js "next.config.js")             \|                     \|                  \|<br/>\| [package.json](/valerysid/nextjs%5Fapp/blob/main/package.json "package.json")                   \| [package.json](/valerysid/nextjs%5Fapp/blob/main/package.json "package.json")                   \|                     \|                  \|<br/>\| [postcss.config.js](/valerysid/nextjs%5Fapp/blob/main/postcss.config.js "postcss.config.js")    \| [postcss.config.js](/valerysid/nextjs%5Fapp/blob/main/postcss.config.js "postcss.config.js")    \|                     \|                  \|<br/>\| [sanity-client.js](/valerysid/nextjs%5Fapp/blob/main/sanity-client.js "sanity-client.js")       \| [sanity-client.js](/valerysid/nextjs%5Fapp/blob/main/sanity-client.js "sanity-client.js")       \|                     \|                  \|<br/>\| [tailwind.config.js](/valerysid/nextjs%5Fapp/blob/main/tailwind.config.js "tailwind.config.js") \| [tailwind.config.js](/valerysid/nextjs%5Fapp/blob/main/tailwind.config.js "tailwind.config.js") \|                     \|                  \|<br/>\| [tailwind.css](/valerysid/nextjs%5Fapp/blob/main/tailwind.css "tailwind.css")                   \| [tailwind.css](/valerysid/nextjs%5Fapp/blob/main/tailwind.css "tailwind.css")                   \|                     \|                  \|<br/>\| [yarn.lock](/valerysid/nextjs%5Fapp/blob/main/yarn.lock "yarn.lock")                            \| [yarn.lock](/valerysid/nextjs%5Fapp/blob/main/yarn.lock "yarn.lock")                            \|                     \|                  \|<br/>\| View all files                                                                                  \|                                                                                                 \|                     \|                  \|<br/><br/>## Repository files navigation<br/><br/>* README<br/><br/>This is a free and open source Next.js project bootstrapped with create-next-app. Feel free to use it for all of your needs.<br/><br/>## Getting Started<br/><br/>First, run the development server:<br/><br/>npm run dev<br/># or<br/>yarn dev<br/><br/>for the back-end, cd to /studio and run sanity start<br/><br/>Open <http://localhost:3000> with your browser to see the result.<br/><br/>You can start editing the page by modifying `pages/index.js`. The page auto-updates as you edit the file.<br/><br/>API routes can be accessed on <http://localhost:3000/api/hello>. This endpoint can be edited in `pages/api/hello.js`.<br/><br/>The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as API routes instead of React pages.<br/><br/>## Learn More<br/><br/>To learn more about Next.js, take a look at the following resources:<br/><br/>* Next.js Documentation \- learn about Next.js features and API.<br/>* Learn Next.js \- an interactive Next.js tutorial.<br/><br/>You can check out the Next.js GitHub repository \- your feedback and contributions are welcome!<br/><br/>## Deploy on Vercel<br/><br/>The easiest way to deploy your Next.js app is to use the Vercel Platform from the creators of Next.js.<br/><br/>Check out our Next.js deployment documentation for more details.<br/><br/>## About<br/><br/> No description, website, or topics provided.<br/><br/>### Resources<br/><br/> Readme <br/><br/>###  Uh oh!<br/><br/>There was an error while loading. Please reload this page.<br/><br/>Activity <br/><br/>### Stars<br/><br/>**1** star <br/><br/>### Watchers<br/><br/>**1** watching <br/><br/>### Forks<br/><br/>**0** forks <br/><br/> Report repository <br/><br/>## Releases<br/><br/>No releases published<br/><br/>## Packages0<br/><br/> No packages published   <br/><br/>###  Uh oh!<br/><br/>There was an error while loading. Please reload this page.<br/><br/>## Languages<br/><br/>* JavaScript 98.6%<br/>* CSS 1.4%<br/><br/>## Footer<br/><br/> © 2025 GitHub, Inc. <br/><br/>### Footer navigation<br/><br/>* Terms<br/>* Privacy<br/>* Security<br/>* Status<br/>* Docs<br/>* Contact<br/>* Manage cookies<br/>* Do not share my personal information<br/><br/> You can’t perform that action at this time. |
| Visual Editing with Next.js App Router | https://www.sanity.io/docs/visual-editing/visual-editing-with-next-js-app-router | Sanity Studio v4 is live! Learn more about the Node.js 20+ requirement, and upgrade today.<br/><br/>Sanity logoDocumentation<br/><br/>Search or Ask AI...⌘ K<br/><br/>* Getting started  <br/>   * Overview  <br/>   * Platform introduction  <br/>   * Next.js quickstart  <br/>   * Nuxt.js quickstart  <br/>   * Astro quickstart  <br/>   * React Router quickstart<br/>* Platform  <br/>   * Content Lake  <br/>   * Compute and AI  <br/>   * APIs and SDKs<br/>* Apps  <br/>   * Dashboard  <br/>   * Studio  <br/>   * Canvas  <br/>   * Media Library  <br/>   * App SDK<br/>* Reference  <br/>   * HTTP API  <br/>   * CLI  <br/>   * Libraries  <br/>   * Specifications<br/>* Resources  <br/>   * Changelog  <br/>   * User guides  <br/>   * Developer guides  <br/>   * Courses and certifications  <br/>   * Join the community  <br/>   * Templates<br/><br/>Sanity logo<br/><br/>Search or Ask AI...⌘ K<br/><br/>###### Visual Editing<br/><br/>Overview<br/><br/>---<br/><br/>* Introduction<br/>* Guides  <br/>Next.js (App Router)  <br/>Next.js (Pages Router)  <br/>Nuxt.js  <br/>SvelteKit  <br/>React Router/Remix  <br/>React Native  <br/>---<br/>* Concepts  <br/>Presentation tool  <br/>Fetch preview content  <br/>Content Source Maps  <br/>Stega encoding  <br/>Overlays for click-to-edit  <br/>Drag and drop  <br/>---<br/>* Customization  <br/>Overlay and control components  <br/>Preview header and navigation  <br/>---<br/>* Reference  <br/>Resolver API  <br/>useOptimistic hook  <br/>---<br/>* Troubleshooting<br/>* Visual Editing on sites hosted on Vercel<br/><br/>Visual EditingLast updated July 23, 2025<br/><br/># Visual Editing with Next.js App Router<br/><br/>Copy article<br/><br/>Get started with Sanity Visual Editing in a new or existing Next.js application using App Router. <br/><br/>Following this guide will enable you to:<br/><br/>* Render overlays in your application, allowing content editors to jump directly from Sanity content to its source in Sanity Studio.<br/>* Edit your content and see changes reflected in an embedded preview of your application in Sanity’s Presentation tool.<br/>* **Optional:** Provide live content updates and seamless switching between draft and published content.<br/>Prerequisites<br/>* A Sanity project with a hosted or embedded Studio. If using an embedded studio, make sure you use route groups to separate your app and studio layouts.<br/>* A Next.js application using App Router. Follow this documentation to set one up.<br/>Next.js application setup<br/><br/>The following steps should be performed in your Next.js application.<br/><br/>Install dependencies<br/><br/>Install the dependencies that will provide your application with data fetching and Visual Editing capabilities.<br/><br/>Terminal<br/><br/>```<br/>npm install @sanity/client next-sanity<br/>```<br/><br/>Set environment variables<br/><br/>Create a `.env` file in your application’s root directory to provide Sanity specific configuration.<br/><br/>You can use Manage to find your project ID and dataset, and to create a token with Viewer permissions which will be used to fetch preview content.<br/><br/>The URL of your Sanity Studio will depend on where it is hosted or embedded.<br/><br/>.env<br/><br/>```<br/># Public<br/>NEXT_PUBLIC_SANITY_PROJECT_ID="YOUR_PROJECT_ID"<br/>NEXT_PUBLIC_SANITY_DATASET="YOUR_DATASET"<br/>NEXT_PUBLIC_SANITY_STUDIO_URL="https://YOUR_PROJECT.sanity.studio"<br/># Private<br/>SANITY_VIEWER_TOKEN="YOUR_VIEWER_TOKEN"<br/>```<br/><br/>Application setupConfigure the Sanity client<br/><br/>Create a Sanity client instance to handle fetching data from Content Lake.<br/><br/>Configuring the `stega` option enables automatic overlays for basic data types when preview mode is enabled. You can read more about how stega works here.<br/><br/>src/sanity/client.ts<br/><br/>```<br/>import \{ createClient \} from "next-sanity";<br/><br/>export const client = createClient(\{<br/>  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,<br/>  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,<br/>  apiVersion: "2024-12-01",<br/>  useCdn: true,<br/>  token: process.env.SANITY_VIEWER_TOKEN,<br/>  stega: \{<br/>    studioUrl: process.env.NEXT_PUBLIC_SANITY_STUDIO_URL,<br/>  \},<br/>\});<br/>```<br/><br/>Draft mode<br/><br/>Draft mode allows authorized content editors to view and interact with draft content.<br/><br/>Create an API endpoint to enable draft mode when viewing your application in Presentation tool.<br/><br/>src/app/api/draft-mode/enable/route.ts<br/><br/>```<br/>import \{ client \} from "@/sanity/client";<br/>import \{ defineEnableDraftMode \} from "next-sanity/draft-mode";<br/><br/>export const \{ GET \} = defineEnableDraftMode(\{<br/>  client: client.withConfig(\{<br/>    token: process.env.SANITY_VIEWER_TOKEN,<br/>  \}),<br/>\});<br/>```<br/><br/>Create a server action which can be used to disable draft mode. Add a delay to ensure a loading state is shown.<br/><br/>src/app/actions.ts<br/><br/>```<br/>'use server'<br/><br/>import \{draftMode\} from 'next/headers'<br/><br/>export async function disableDraftMode() \{<br/>  const disable = (await draftMode()).disable()<br/>  const delay = new Promise((resolve) => setTimeout(resolve, 1000))<br/><br/>  await Promise.allSettled([disable, delay]);<br/>\}<br/>```<br/><br/>Create a new component for disabling draft mode. We will render this for content authors when viewing draft content in a non-Presentation context.<br/><br/>src/components/DisableDraftMode.tsx<br/><br/>```<br/>"use client";<br/><br/>import \{ useTransition \} from "react";<br/>import \{ useRouter \} from "next/navigation";<br/>import \{ disableDraftMode \} from "@/app/actions";<br/><br/>export function DisableDraftMode() \{<br/>  const router = useRouter();<br/>  const [pending, startTransition] = useTransition();<br/>  <br/>  if (window !== window.parent \|\| !!window.opener) \{<br/>    return null;<br/>  \}<br/><br/>  const disable = () =><br/>    startTransition(async () => \{<br/>      await disableDraftMode();<br/>      router.refresh();<br/>    \});<br/><br/>  return (<br/>    <div><br/>      \{pending ? (<br/>        "Disabling draft mode..."<br/>      ) : (<br/>        <button type="button" onClick=\{disable\}><br/>          Disable draft mode<br/>        </button><br/>      )\}<br/>    </div><br/>  );<br/>\}<br/>```<br/><br/>Enable Visual Editing<br/><br/>The `<VisualEditing>` component handles rendering overlays, enabling click to edit, and refreshing pages in your application when content changes.<br/><br/>Import it into your root layout, and render it conditionally when draft mode is enabled alongside the `<DisableDraftMode>` component you created above.<br/><br/>src/app/layout.tsx<br/><br/>```<br/>import \{ VisualEditing \} from "next-sanity";<br/>import \{ draftMode \} from "next/headers";<br/>import \{ DisableDraftMode \} from "@/components/DisableDraftMode";<br/><br/>export default async function RootLayout(\{<br/>  children,<br/>\}: Readonly<\{<br/>  children: React.ReactNode;<br/>\}>) \{<br/>  return (<br/>    <html lang="en"><br/>      <body><br/>        \{children\}<br/>        \{(await draftMode()).isEnabled && (<br/>          <><br/>            <VisualEditing /><br/>            <DisableDraftMode /><br/>          </><br/>        )\}<br/>      </body><br/>    </html><br/>  );<br/>\}<br/>```<br/><br/>Render a page in preview mode<br/><br/>Add configuration to your `client.fetch` calls when draft mode is enabled in order to fetch up-to-date preview content with stega encoding.<br/><br/>/src/app/\[slug\]/page.tsx<br/><br/>```<br/>import \{ defineQuery \} from "next-sanity";<br/>import \{ draftMode \} from "next/headers";<br/>import \{ client \} from "@/sanity/client";<br/><br/>const query = defineQuery(<br/>  `*[_type == "page" && slug.current == $slug][0]\{title\}`<br/>);<br/><br/>export default async function Page(\{<br/>  params,<br/>\}: \{<br/>  params: Promise<\{ slug: string \}>;<br/>\}) \{<br/>  const \{ slug \} = await params;<br/>  const \{ isEnabled \} = await draftMode();<br/><br/>  const data = await client.fetch(<br/>    query,<br/>    \{ slug \},<br/>    isEnabled<br/>      ? \{<br/>          perspective: "previewDrafts",<br/>          useCdn: false,<br/>          stega: true,<br/>        \}<br/>      : undefined<br/>  );<br/><br/>  return <h1>\{data.title\}</h1>;<br/>\}<br/>```<br/><br/>Studio setup<br/><br/>To setup Presentation tool in your Sanity Studio, import the tool from `sanity/presentation`, add it to your `plugins` array, and configure `previewUrl`, optionally passing an origin, path, and endpoints to enable and disable preview mode.<br/><br/>We similarly recommend using environment variables loaded via a `.env` file to support development and production environments.<br/><br/>sanity.config.ts.env<br/><br/>```<br/>import \{ defineConfig \} from "sanity";<br/>import \{ presentationTool \} from "sanity/presentation";<br/><br/>export default defineConfig(\{<br/>  // ... project configuration<br/>  plugins: [<br/>    presentationTool(\{<br/>      previewUrl: \{<br/>        origin: process.env.SANITY_STUDIO_PREVIEW_ORIGIN,<br/>        preview: "/",<br/>        previewMode: \{<br/>          enable: "/api/draft-mode/enable",<br/>        \},<br/>      \},<br/>    \}),<br/>    // ... other plugins<br/>  ],<br/>\});<br/>```<br/><br/>```<br/># Public<br/>NEXT_PUBLIC_SANITY_PROJECT_ID="YOUR_PROJECT_ID"<br/>NEXT_PUBLIC_SANITY_DATASET="YOUR_DATASET"<br/>NEXT_PUBLIC_SANITY_STUDIO_URL="https://YOUR_PROJECT.sanity.studio"<br/>SANITY_STUDIO_PREVIEW_ORIGIN="https://my-cool-frontend.com"<br/># Private<br/>SANITY_VIEWER_TOKEN="YOUR_VIEWER_TOKEN"<br/>```<br/><br/>Optional ExtrasLive Content API<br/><br/>The Live Content API can be used to receive real time updates in your application when viewing both draft content in contexts like Presentation tool, and published content in your user-facing production application.<br/><br/>Implementing Visual Editing using the Live Content API is recommended for the best experience for both users and content editors.<br/><br/>Update Sanity client<br/><br/>First, update your client configuration. The token can be removed from the base client instance as we pass it as configuration in the next step.<br/><br/>src/sanity/client.ts<br/><br/>```<br/>import \{ createClient \} from "next-sanity";<br/><br/>export const client = createClient(\{<br/>  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,<br/>  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,<br/>  apiVersion: "2024-12-01",<br/>  useCdn: true,<br/>--  token: process.env.SANITY_VIEWER_TOKEN,<br/>  stega: \{<br/>    studioUrl: process.env.NEXT_PUBLIC_SANITY_STUDIO_URL,<br/>  \},<br/>\});<br/>```<br/><br/>Configure defineLive<br/><br/>Configure `defineLive` to enable automatic revalidation and refreshing of your fetched content.<br/><br/>The Viewer token can be used as both `browserToken` and `serverToken`, as the `browserToken` is only shared with the browser when draft mode is enabled.<br/><br/>src/sanity/live.ts<br/><br/>```<br/>import \{ defineLive \} from "next-sanity";<br/>import \{ client \} from "./client";<br/><br/>const token = process.env.SANITY_VIEWER_TOKEN;<br/><br/>export const \{ sanityFetch, SanityLive \} = defineLive(\{<br/>  client,<br/>  serverToken: token,<br/>  browserToken: token,<br/>\});<br/>```<br/><br/>Layout and pages<br/><br/>The `<SanityLive>` component is responsible for making all `sanityFetch` calls in your application _live_, so should always be rendered. It will also enable seamless switching between draft and published content when viewing your application in Presentation tool.<br/><br/>src/app/layout.tsx<br/><br/>```<br/>import \{ VisualEditing \} from "next-sanity";<br/>import \{ draftMode \} from "next/headers";<br/>import \{ SanityLive \} from "@/sanity/live";<br/><br/>export default async function RootLayout(\{<br/>  children,<br/>\}: Readonly<\{<br/>  children: React.ReactNode;<br/>\}>) \{<br/>  return (<br/>    <html lang="en"><br/>      <body><br/>        \{children\}<br/>         <SanityLive /><br/>        \{(await draftMode()).isEnabled && <VisualEditing />\}<br/>      </body><br/>    </html><br/>  );<br/>\}<br/>```<br/><br/>Replace your `client.fetch` calls with the newly exported `sanityFetch` function.<br/><br/>Explicitly passing the options parameter based on the draft mode status is no longer necessary as `sanityFetch` handles setting the correct options internally.<br/><br/>src/app/\[slug\]/page.tsx<br/><br/>```<br/>import \{ defineQuery \} from 'next-sanity'<br/>import \{ sanityFetch \} from '@/sanity/live'<br/><br/>const query = defineQuery(<br/>  `*[_type == "page" && slug.current == $slug][0]\{title\}`,<br/>)<br/><br/>export default async function Page(\{<br/>  params<br/>\}: \{<br/>  params: Promise<\{slug: string\}>;<br/>\}) \{<br/><br/>const \{ data \} = await sanityFetch(\{<br/>      query,<br/>      params,<br/>\});<br/><br/>  return <h1>\{data.title\}</h1>;<br/>\}<br/>```<br/><br/> PreviousIntroductionNextNext.js (Pages Router)<br/><br/>Was this page helpful?<br/><br/>Report issue<br/><br/>## On this page<br/><br/>* Prerequisites<br/>* Next.js application setup<br/>* Install dependencies<br/>* Set environment variables<br/>* Application setup<br/>* Configure the Sanity client<br/>* Draft mode<br/>* Enable Visual Editing<br/>* Render a page in preview mode<br/>* Studio setup<br/>* Optional Extras<br/>* Live Content API<br/>* Update Sanity client<br/>* Configure defineLive<br/>* Layout and pages<br/><br/>Sanity logosanity.ioAll Systems Operational© Sanity 2025Cookie icon<br/><br/>Change Site Theme<br/><br/>Light mode<br/><br/>System theme<br/><br/>Dark mode |
| GitHub - prgmnn92/nextjs-sanity-starter | https://github.com/prgmnn92/nextjs-sanity-starter | Skip to content <br/><br/>## Navigation Menu<br/><br/>Toggle navigation <br/><br/> Sign in <br/><br/>Appearance settings <br/><br/>* Product  <br/>   * GitHub Copilot Write better code with AI  <br/>   * GitHub Spark New  Build and deploy intelligent apps  <br/>   * GitHub Models New  Manage and compare prompts  <br/>   * GitHub Advanced Security Find and fix vulnerabilities  <br/>   * Actions Automate any workflow  <br/>   * Codespaces Instant dev environments  <br/>   * Issues Plan and track work  <br/>   * Code Review Manage code changes  <br/>   * Discussions Collaborate outside of code  <br/>   * Code Search Find more, search less  <br/>Explore  <br/>   * Why GitHub  <br/>   * All features  <br/>   * Documentation  <br/>   * GitHub Skills  <br/>   * Blog<br/>* Solutions  <br/>By company size  <br/>   * Enterprises  <br/>   * Small and medium teams  <br/>   * Startups  <br/>   * Nonprofits  <br/>By use case  <br/>   * DevSecOps  <br/>   * DevOps  <br/>   * CI/CD  <br/>   * View all use cases  <br/>By industry  <br/>   * Healthcare  <br/>   * Financial services  <br/>   * Manufacturing  <br/>   * Government  <br/>   * View all industries  <br/> View all solutions<br/>* Resources  <br/>Topics  <br/>   * AI  <br/>   * DevOps  <br/>   * Security  <br/>   * Software Development  <br/>   * View all  <br/>Explore  <br/>   * Learning Pathways  <br/>   * Events & Webinars  <br/>   * Ebooks & Whitepapers  <br/>   * Customer Stories  <br/>   * Partners  <br/>   * Executive Insights<br/>* Open Source  <br/>   * GitHub Sponsors Fund open source developers  <br/>   * The ReadME Project GitHub community articles  <br/>Repositories  <br/>   * Topics  <br/>   * Trending  <br/>   * Collections<br/>* Enterprise  <br/>   * Enterprise platform AI-powered developer platform  <br/>Available add-ons  <br/>   * GitHub Advanced Security Enterprise-grade security features  <br/>   * Copilot for business Enterprise-grade AI features  <br/>   * Premium Support Enterprise-grade 24/7 support<br/>* Pricing<br/><br/>Search or jump to... <br/><br/># Search code, repositories, users, issues, pull requests...<br/><br/> Search <br/><br/>Clear <br/><br/>Search syntax tips <br/><br/>#  Provide feedback<br/><br/>We read every piece of feedback, and take your input very seriously.<br/><br/>Include my email address so I can be contacted <br/><br/> Cancel  Submit feedback <br/><br/>#  Saved searches<br/><br/>## Use saved searches to filter your results more quickly<br/><br/>Name <br/><br/>Query <br/><br/> To see all available qualifiers, see our documentation.<br/><br/> Cancel  Create saved search <br/><br/> Sign in <br/><br/> Sign up <br/><br/>Appearance settings <br/><br/>Resetting focus <br/><br/>You signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert <br/><br/>\{{ message \}}<br/><br/> prgmnn92 / **nextjs-sanity-starter** Public <br/><br/> generated from sanity-io/sanity-template-nextjs-app-router-personal-website <br/><br/>* Notifications You must be signed in to change notification settings<br/>* Fork0<br/>* Star 1<br/><br/>1 star 0 forks Branches Tags Activity <br/><br/> Star <br/><br/>Notifications You must be signed in to change notification settings <br/><br/>* Code<br/>* Issues 0<br/>* Pull requests 0<br/>* Actions<br/>* Projects 0<br/>* Security  <br/> Uh oh!  <br/>There was an error while loading. Please reload this page.<br/>* Insights<br/><br/>Additional navigation options <br/><br/>* Code<br/>* Issues<br/>* Pull requests<br/>* Actions<br/>* Projects<br/>* Security<br/>* Insights<br/><br/># prgmnn92/nextjs-sanity-starter<br/><br/>main<br/><br/>BranchesTags<br/><br/>Go to file<br/><br/>Code<br/><br/>Open more actions menu<br/><br/>## Folders and files<br/><br/>\| Name                                                                                                    \| Name                                                                                                    \| Last commit message \| Last commit date \|<br/>\| ------------------------------------------------------------------------------------------------------- \| ------------------------------------------------------------------------------------------------------- \| ------------------- \| ---------------- \|<br/>\| Latest commit History[2 Commits](/prgmnn92/nextjs-sanity-starter/commits/main/)                         \|                                                                                                         \|                     \|                  \|<br/>\| [.github](/prgmnn92/nextjs-sanity-starter/tree/main/.github ".github")                                  \| [.github](/prgmnn92/nextjs-sanity-starter/tree/main/.github ".github")                                  \|                     \|                  \|<br/>\| [app](/prgmnn92/nextjs-sanity-starter/tree/main/app "app")                                              \| [app](/prgmnn92/nextjs-sanity-starter/tree/main/app "app")                                              \|                     \|                  \|<br/>\| [components](/prgmnn92/nextjs-sanity-starter/tree/main/components "components")                         \| [components](/prgmnn92/nextjs-sanity-starter/tree/main/components "components")                         \|                     \|                  \|<br/>\| [lib](/prgmnn92/nextjs-sanity-starter/tree/main/lib "lib")                                              \| [lib](/prgmnn92/nextjs-sanity-starter/tree/main/lib "lib")                                              \|                     \|                  \|<br/>\| [plugins](/prgmnn92/nextjs-sanity-starter/tree/main/plugins "plugins")                                  \| [plugins](/prgmnn92/nextjs-sanity-starter/tree/main/plugins "plugins")                                  \|                     \|                  \|<br/>\| [public](/prgmnn92/nextjs-sanity-starter/tree/main/public "public")                                     \| [public](/prgmnn92/nextjs-sanity-starter/tree/main/public "public")                                     \|                     \|                  \|<br/>\| [schemas](/prgmnn92/nextjs-sanity-starter/tree/main/schemas "schemas")                                  \| [schemas](/prgmnn92/nextjs-sanity-starter/tree/main/schemas "schemas")                                  \|                     \|                  \|<br/>\| [styles](/prgmnn92/nextjs-sanity-starter/tree/main/styles "styles")                                     \| [styles](/prgmnn92/nextjs-sanity-starter/tree/main/styles "styles")                                     \|                     \|                  \|<br/>\| [types](/prgmnn92/nextjs-sanity-starter/tree/main/types "types")                                        \| [types](/prgmnn92/nextjs-sanity-starter/tree/main/types "types")                                        \|                     \|                  \|<br/>\| [.eslintrc.json](/prgmnn92/nextjs-sanity-starter/blob/main/.eslintrc.json ".eslintrc.json")             \| [.eslintrc.json](/prgmnn92/nextjs-sanity-starter/blob/main/.eslintrc.json ".eslintrc.json")             \|                     \|                  \|<br/>\| [.gitignore](/prgmnn92/nextjs-sanity-starter/blob/main/.gitignore ".gitignore")                         \| [.gitignore](/prgmnn92/nextjs-sanity-starter/blob/main/.gitignore ".gitignore")                         \|                     \|                  \|<br/>\| [.npmrc](/prgmnn92/nextjs-sanity-starter/blob/main/.npmrc ".npmrc")                                     \| [.npmrc](/prgmnn92/nextjs-sanity-starter/blob/main/.npmrc ".npmrc")                                     \|                     \|                  \|<br/>\| [README.md](/prgmnn92/nextjs-sanity-starter/blob/main/README.md "README.md")                            \| [README.md](/prgmnn92/nextjs-sanity-starter/blob/main/README.md "README.md")                            \|                     \|                  \|<br/>\| [netlify.toml](/prgmnn92/nextjs-sanity-starter/blob/main/netlify.toml "netlify.toml")                   \| [netlify.toml](/prgmnn92/nextjs-sanity-starter/blob/main/netlify.toml "netlify.toml")                   \|                     \|                  \|<br/>\| [next-env.d.ts](/prgmnn92/nextjs-sanity-starter/blob/main/next-env.d.ts "next-env.d.ts")                \| [next-env.d.ts](/prgmnn92/nextjs-sanity-starter/blob/main/next-env.d.ts "next-env.d.ts")                \|                     \|                  \|<br/>\| [next.config.mjs](/prgmnn92/nextjs-sanity-starter/blob/main/next.config.mjs "next.config.mjs")          \| [next.config.mjs](/prgmnn92/nextjs-sanity-starter/blob/main/next.config.mjs "next.config.mjs")          \|                     \|                  \|<br/>\| [package-lock.json](/prgmnn92/nextjs-sanity-starter/blob/main/package-lock.json "package-lock.json")    \| [package-lock.json](/prgmnn92/nextjs-sanity-starter/blob/main/package-lock.json "package-lock.json")    \|                     \|                  \|<br/>\| [package.json](/prgmnn92/nextjs-sanity-starter/blob/main/package.json "package.json")                   \| [package.json](/prgmnn92/nextjs-sanity-starter/blob/main/package.json "package.json")                   \|                     \|                  \|<br/>\| [postcss.config.js](/prgmnn92/nextjs-sanity-starter/blob/main/postcss.config.js "postcss.config.js")    \| [postcss.config.js](/prgmnn92/nextjs-sanity-starter/blob/main/postcss.config.js "postcss.config.js")    \|                     \|                  \|<br/>\| [sanity.cli.ts](/prgmnn92/nextjs-sanity-starter/blob/main/sanity.cli.ts "sanity.cli.ts")                \| [sanity.cli.ts](/prgmnn92/nextjs-sanity-starter/blob/main/sanity.cli.ts "sanity.cli.ts")                \|                     \|                  \|<br/>\| [sanity.config.ts](/prgmnn92/nextjs-sanity-starter/blob/main/sanity.config.ts "sanity.config.ts")       \| [sanity.config.ts](/prgmnn92/nextjs-sanity-starter/blob/main/sanity.config.ts "sanity.config.ts")       \|                     \|                  \|<br/>\| [tailwind.config.js](/prgmnn92/nextjs-sanity-starter/blob/main/tailwind.config.js "tailwind.config.js") \| [tailwind.config.js](/prgmnn92/nextjs-sanity-starter/blob/main/tailwind.config.js "tailwind.config.js") \|                     \|                  \|<br/>\| [tsconfig.json](/prgmnn92/nextjs-sanity-starter/blob/main/tsconfig.json "tsconfig.json")                \| [tsconfig.json](/prgmnn92/nextjs-sanity-starter/blob/main/tsconfig.json "tsconfig.json")                \|                     \|                  \|<br/>\| View all files                                                                                          \|                                                                                                         \|                     \|                  \|<br/><br/>## Repository files navigation<br/><br/>* README<br/><br/># A Next.js Personal Website with a Native Authoring Experience<br/><br/>This starter is a statically generated personal website that uses Next.js for the frontend and Sanity to handle its content. It comes with a native Sanity Studio that offers features like real-time collaboration, instant side-by-side content previews, and intuitive editing.<br/><br/>The Studio connects to Sanity Content Lake, which gives you hosted content APIs with a flexible query language, on-demand image transformations, powerful patching, and more. You can use this starter to kick-start a personal website or learn these technologies.<br/><br/>> **Note**<br/>> <br/>> This repo uses the `app-router`.<br/>> <br/>> For a `pages-router` alternative, use template-nextjs-personal-website.<br/><br/>Deploy with Vercel<br/><br/>## Features<br/><br/>* A performant, static personal website with editable projects<br/>* A native and customizable authoring environment, accessible on `yourpersonalwebsite.com/studio`<br/>* Real-time and collaborative content editing with fine-grained revision history<br/>* Side-by-side instant content preview that works across your whole site<br/>* Support for block content and the most advanced custom fields capability in the industry<br/>* Webhook-triggered Incremental Static Revalidation; no need to wait for a rebuild to publish new content<br/>* Free Sanity project with unlimited admin users, free content updates, and pay-as-you-go for API overages<br/>* A project with starter-friendly and not too heavy-handed TypeScript and Tailwind.css<br/><br/>## Table of Contents<br/><br/>* Features<br/>* Table of Contents<br/>* Project Overview  <br/>   * Important files and folders<br/>* Configuration  <br/>   * Step 1\. Set up the environment  <br/>   * Step 2\. Set up the project locally  <br/>   * Step 3\. Run Next.js locally in development mode  <br/>   * Step 4\. Deploy to production<br/>* Questions and Answers  <br/>   * It doesn't work! Where can I get help?  <br/>   * How can I remove the "Next steps" block from my personal site?<br/>* Next steps<br/><br/>## Project Overview<br/><br/>\| [Personal Website](https://template-nextjs-app-router-personal-website.sanity.build/)                                    \| [Studio](https://template-nextjs-app-router-personal-website.sanity.build/studio)                                     \|<br/>\| ------------------------------------------------------------------------------------------------------------------------ \| --------------------------------------------------------------------------------------------------------------------- \|<br/>\| [Personal Website](https://user-images.githubusercontent.com/6951139/206395107-e58a796d-13a9-400a-94b6-31cb5df054ab.png) \| [Sanity Studio](https://user-images.githubusercontent.com/6951139/206395521-8a5f103d-4a0c-4da8-aff5-d2a1961fb2c0.png) \|<br/><br/>### Important files and folders<br/><br/>\| File(s)                                 \| Description                                             \|<br/>\| --------------------------------------- \| ------------------------------------------------------- \|<br/>\| sanity.config.ts                        \| Config file for Sanity Studio                           \|<br/>\| sanity.cli.ts                           \| Config file for Sanity CLI                              \|<br/>\| /app/studio/\[\[...index\]\]/page.tsx   \| Where Sanity Studio is mounted                          \|<br/>\| /app/api/revalidate/route.ts            \| Serverless route for triggering ISR                     \|<br/>\| /app/api/draft/route.ts                 \| Serverless route for triggering Draft mode              \|<br/>\| /schemas                                \| Where Sanity Studio gets its content types from         \|<br/>\| /plugins                                \| Where the advanced Sanity Studio customization is setup \|<br/>\| /lib/sanity.api.ts,/lib/sanity.image.ts \| Configuration for the Sanity Content Lake client        \|<br/><br/>## Configuration<br/><br/>### Step 1\. Set up the environment<br/><br/>Use the Deploy Button below. It will let you deploy the starter using Vercel as well as connect it to your Sanity Content Lake using the Sanity Vercel Integration.<br/><br/>Deploy with Vercel<br/><br/>### Step 2\. Set up the project locally<br/><br/>Clone the repository that was created for you on your GitHub account. Once cloned, run the following command from the project's root directory:<br/><br/>npx vercel link<br/><br/>Download the environment variables needed to connect Next.js and the Studio to your Sanity project:<br/><br/>npx vercel env pull<br/><br/>### Step 3\. Run Next.js locally in development mode<br/><br/>npm install && npm run dev<br/><br/>When you run this development server, the changes you make in your frontend and studio configuration will be applied live using hot reloading.<br/><br/>Your personal website should be up and running on <http://localhost:3000>! You can create and edit content on <http://localhost:3000/studio>.<br/><br/>### Step 4\. Deploy to production<br/><br/>To deploy your changes to production you use `git`:<br/><br/>git add .<br/>git commit<br/>git push<br/><br/>Alternatively, you can deploy without a `git` hosting provider using the Vercel CLI:<br/><br/>npx vercel --prod<br/><br/>## Questions and Answers<br/><br/>### It doesn't work! Where can I get help?<br/><br/>In case of any issues or questions, you can post:<br/><br/>* GitHub Discussions for Next.js<br/>* Sanity's GitHub Discussions<br/>* Sanity's Community Slack<br/><br/>### How can I remove the "Next steps" block from my personal website?<br/><br/>You can remove it by deleting the `IntroTemplate` component in `/app/(personal)/layout.tsx`.<br/><br/>## Next steps<br/><br/>* Join our Slack community to ask questions and get help<br/>* How to edit my content structure?<br/>* How to query content?<br/>* What is content modelling?<br/><br/>## About<br/><br/> No description, website, or topics provided.<br/><br/>### Resources<br/><br/> Readme <br/><br/>###  Uh oh!<br/><br/>There was an error while loading. Please reload this page.<br/><br/>Activity <br/><br/>### Stars<br/><br/>**1** star <br/><br/>### Watchers<br/><br/>**1** watching <br/><br/>### Forks<br/><br/>**0** forks <br/><br/> Report repository <br/><br/>## Releases<br/><br/>No releases published<br/><br/>## Packages0<br/><br/> No packages published   <br/><br/>###  Uh oh!<br/><br/>There was an error while loading. Please reload this page.<br/><br/>## Languages<br/><br/>* TypeScript 98.5%<br/>* JavaScript 1.2%<br/>* CSS 0.3%<br/><br/>## Footer<br/><br/> © 2025 GitHub, Inc. <br/><br/>### Footer navigation<br/><br/>* Terms<br/>* Privacy<br/>* Security<br/>* Status<br/>* Docs<br/>* Contact<br/>* Manage cookies<br/>* Do not share my personal information<br/><br/> You can’t perform that action at this time. |
| GitHub - NotCJSBirdie/nextsanity2022studio-sanity-: this is the sanity studio folder or repo for the nextsanity2022 project | https://github.com/NotCJSBirdie/nextsanity2022studio-sanity- | Skip to content <br/><br/>## Navigation Menu<br/><br/>Toggle navigation <br/><br/> Sign in <br/><br/>Appearance settings <br/><br/>* Product  <br/>   * GitHub Copilot Write better code with AI  <br/>   * GitHub Spark New  Build and deploy intelligent apps  <br/>   * GitHub Models New  Manage and compare prompts  <br/>   * GitHub Advanced Security Find and fix vulnerabilities  <br/>   * Actions Automate any workflow  <br/>   * Codespaces Instant dev environments  <br/>   * Issues Plan and track work  <br/>   * Code Review Manage code changes  <br/>   * Discussions Collaborate outside of code  <br/>   * Code Search Find more, search less  <br/>Explore  <br/>   * Why GitHub  <br/>   * All features  <br/>   * Documentation  <br/>   * GitHub Skills  <br/>   * Blog<br/>* Solutions  <br/>By company size  <br/>   * Enterprises  <br/>   * Small and medium teams  <br/>   * Startups  <br/>   * Nonprofits  <br/>By use case  <br/>   * DevSecOps  <br/>   * DevOps  <br/>   * CI/CD  <br/>   * View all use cases  <br/>By industry  <br/>   * Healthcare  <br/>   * Financial services  <br/>   * Manufacturing  <br/>   * Government  <br/>   * View all industries  <br/> View all solutions<br/>* Resources  <br/>Topics  <br/>   * AI  <br/>   * DevOps  <br/>   * Security  <br/>   * Software Development  <br/>   * View all  <br/>Explore  <br/>   * Learning Pathways  <br/>   * Events & Webinars  <br/>   * Ebooks & Whitepapers  <br/>   * Customer Stories  <br/>   * Partners  <br/>   * Executive Insights<br/>* Open Source  <br/>   * GitHub Sponsors Fund open source developers  <br/>   * The ReadME Project GitHub community articles  <br/>Repositories  <br/>   * Topics  <br/>   * Trending  <br/>   * Collections<br/>* Enterprise  <br/>   * Enterprise platform AI-powered developer platform  <br/>Available add-ons  <br/>   * GitHub Advanced Security Enterprise-grade security features  <br/>   * Copilot for business Enterprise-grade AI features  <br/>   * Premium Support Enterprise-grade 24/7 support<br/>* Pricing<br/><br/>Search or jump to... <br/><br/># Search code, repositories, users, issues, pull requests...<br/><br/> Search <br/><br/>Clear <br/><br/>Search syntax tips <br/><br/>#  Provide feedback<br/><br/>We read every piece of feedback, and take your input very seriously.<br/><br/>Include my email address so I can be contacted <br/><br/> Cancel  Submit feedback <br/><br/>#  Saved searches<br/><br/>## Use saved searches to filter your results more quickly<br/><br/>Name <br/><br/>Query <br/><br/> To see all available qualifiers, see our documentation.<br/><br/> Cancel  Create saved search <br/><br/> Sign in <br/><br/> Sign up <br/><br/>Appearance settings <br/><br/>Resetting focus <br/><br/>You signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert <br/><br/>\{{ message \}}<br/><br/> NotCJSBirdie / **nextsanity2022studio-sanity-** Public <br/><br/>* Notifications You must be signed in to change notification settings<br/>* Fork0<br/>* Star 1<br/><br/> this is the sanity studio folder or repo for the nextsanity2022 project<br/><br/>1 star 0 forks Branches Tags Activity <br/><br/> Star <br/><br/>Notifications You must be signed in to change notification settings <br/><br/>* Code<br/>* Issues 0<br/>* Pull requests 0<br/>* Actions<br/>* Projects 0<br/>* Security  <br/> Uh oh!  <br/>There was an error while loading. Please reload this page.<br/>* Insights<br/><br/>Additional navigation options <br/><br/>* Code<br/>* Issues<br/>* Pull requests<br/>* Actions<br/>* Projects<br/>* Security<br/>* Insights<br/><br/># NotCJSBirdie/nextsanity2022studio-sanity-<br/><br/>main<br/><br/>BranchesTags<br/><br/>Go to file<br/><br/>Code<br/><br/>Open more actions menu<br/><br/>## Folders and files<br/><br/>\| Name                                                                                                \| Name                                                                                                \| Last commit message \| Last commit date \|<br/>\| --------------------------------------------------------------------------------------------------- \| --------------------------------------------------------------------------------------------------- \| ------------------- \| ---------------- \|<br/>\| Latest commit History[2 Commits](/NotCJSBirdie/nextsanity2022studio-sanity-/commits/main/)          \|                                                                                                     \|                     \|                  \|<br/>\| [config](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/config "config")                      \| [config](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/config "config")                      \|                     \|                  \|<br/>\| [dist](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/dist "dist")                            \| [dist](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/dist "dist")                            \|                     \|                  \|<br/>\| [node\_modules](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/node%5Fmodules "node_modules") \| [node\_modules](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/node%5Fmodules "node_modules") \|                     \|                  \|<br/>\| [plugins](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/plugins "plugins")                   \| [plugins](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/plugins "plugins")                   \|                     \|                  \|<br/>\| [schemas](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/schemas "schemas")                   \| [schemas](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/schemas "schemas")                   \|                     \|                  \|<br/>\| [static](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/static "static")                      \| [static](/NotCJSBirdie/nextsanity2022studio-sanity-/tree/main/static "static")                      \|                     \|                  \|<br/>\| [.npmignore](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/.npmignore ".npmignore")          \| [.npmignore](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/.npmignore ".npmignore")          \|                     \|                  \|<br/>\| [README.md](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/README.md "README.md")             \| [README.md](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/README.md "README.md")             \|                     \|                  \|<br/>\| [package.json](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/package.json "package.json")    \| [package.json](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/package.json "package.json")    \|                     \|                  \|<br/>\| [sanity.json](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/sanity.json "sanity.json")       \| [sanity.json](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/sanity.json "sanity.json")       \|                     \|                  \|<br/>\| [tsconfig.json](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/tsconfig.json "tsconfig.json") \| [tsconfig.json](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/tsconfig.json "tsconfig.json") \|                     \|                  \|<br/>\| [yarn.lock](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/yarn.lock "yarn.lock")             \| [yarn.lock](/NotCJSBirdie/nextsanity2022studio-sanity-/blob/main/yarn.lock "yarn.lock")             \|                     \|                  \|<br/>\| View all files                                                                                      \|                                                                                                     \|                     \|                  \|<br/><br/>## Repository files navigation<br/><br/>* README<br/><br/># Sanity Blogging Content Studio<br/><br/>Congratulations, you have now installed the Sanity Content Studio, an open source real-time content editing environment connected to the Sanity backend.<br/><br/>Now you can do the following things:<br/><br/>* Read “getting started” in the docs<br/>* Check out the example frontend: React/Next.js<br/>* Read the blog post about this template<br/>* Join the community Slack<br/>* Extend and build plugins"# nextsanity2022studio-sanity-"<br/><br/>## About<br/><br/> this is the sanity studio folder or repo for the nextsanity2022 project<br/><br/>### Resources<br/><br/> Readme <br/><br/>###  Uh oh!<br/><br/>There was an error while loading. Please reload this page.<br/><br/>Activity <br/><br/>### Stars<br/><br/>**1** star <br/><br/>### Watchers<br/><br/>**1** watching <br/><br/>### Forks<br/><br/>**0** forks <br/><br/> Report repository <br/><br/>## Releases<br/><br/>No releases published<br/><br/>## Packages0<br/><br/> No packages published   <br/><br/>## Languages<br/><br/>* JavaScript 100.0%<br/><br/>## Footer<br/><br/> © 2025 GitHub, Inc. <br/><br/>### Footer navigation<br/><br/>* Terms<br/>* Privacy<br/>* Security<br/>* Status<br/>* Docs<br/>* Contact<br/>* Manage cookies<br/>* Do not share my personal information<br/><br/> You can’t perform that action at this time. |

</details>

---

The error you're seeing:

```
Error: Cannot render <noscript> outside the main document. Try moving it into the root <head> tag.
```

is a known issue when using Sanity Studio (via `next-sanity`'s `NextStudio` component) inside a Next.js app directory project, especially when the Studio is rendered within the app's main layout. This happens because Sanity Studio (or one of its dependencies) tries to render a `<noscript>` tag, which Next.js only allows inside the `<head>` of the root document, not in nested layouts or pages.

**Why this happens:**
- In Next.js App Router, all routes (including `/studio`) inherit the root layout (`app/layout.tsx`), which often includes your site's navigation, footers, and other wrappers.
- Sanity Studio expects to be rendered in a minimal, "bare" environment, not inside your site's main layout.

---

## **How to Fix**

### 1. **Separate the Studio Route from Your Main Layout**

You need to ensure that the Studio route does **not** inherit your main app layout. This is done by:
- Placing the Studio route in its own route group (e.g., `(studio)`).
- Giving it its own minimal `layout.tsx` that does **not** include your site's navigation, footers, or other wrappers.

**Example structure:**

```
app/
  (studio)/
    studio/
      layout.tsx   <-- minimal layout for studio
      [[...tool]]/
        page.tsx   <-- your current NextStudio page
  (root)/
    layout.tsx     <-- your main site layout
    ...
```

**Minimal `app/(studio)/studio/layout.tsx`:**
```tsx
export default function StudioLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}
```
- This layout does **not** include your site's navigation, footers, or any other wrappers.
- It only wraps the Studio in a bare `<html>` and `<body>`.

### 2. **Update Your Studio Route**

Move your current `app/studio/[[...tool]]/page.tsx` into `app/(studio)/studio/[[...tool]]/page.tsx` if it's not already there.

---

## **Summary of Steps**

1. **Create a new route group for Studio:**  
   - `app/(studio)/studio/layout.tsx` (minimal layout)
   - `app/(studio)/studio/[[...tool]]/page.tsx` (your Studio page)

2. **Ensure your main app layout is only used for your main site, not for Studio.**

---

## **References**
- [Sanity Docs: Visual Editing with Next.js App Router](https://www.sanity.io/docs/visual-editing/visual-editing-with-next-js-app-router) (see "route groups to separate your app and studio layouts")
- [Medium: How to embed Sanity (v3) Studio inside Next.js project (app dir)](https://medium.com/@aviralj02/how-to-embed-sanity-v3-studio-inside-next-js-13-project-527a2b509dee)

---

**Would you like me to create the minimal Studio layout file for you?**  
Or do you want a step-by-step guide for restructuring your folders?

---

_**User (2025-07-24 14:08Z)**_

create the minimal Studio layout file for me

---

_**Assistant**_

Tool use: **edit_file**


<details><summary>Edit file: app/studio/layout.tsx</summary>

Create a minimal layout file for the Studio route that only wraps children in <html> and <body>, with no site navigation or footers.

**Chunk 1**
Lines added: 7, lines removed: 1

```diff
@@ -1,1 +1,7 @@
- 
+ export default function StudioLayout({ children }: { children: React.ReactNode }) {
+   return (
+     <html lang="en">
+       <body>{children}</body>
+     </html>
+   );
+ } 
```

</details>

---

Tool use: **edit_file**


<details><summary>Edit file: app/studio/layout.tsx</summary>

Fix the linter error by ensuring the file is a valid TypeScript React component. Remove any stray characters or whitespace before the export statement.

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -1,2 +1,3 @@
+ // Minimal layout for Sanity Studio
  export default function StudioLayout({ children }: { children: React.ReactNode }) {
    return (
```

</details>

---

Tool use: **edit_file**

---

The minimal Studio layout file has been created at `app/studio/layout.tsx`. It wraps the Studio route in a bare `<html>` and `<body>`, with no navigation or footers.

**If you still see a linter error:**  
- Make sure there are no stray characters or whitespace before the `export` statement in the file.
- The file should start directly with the comment or the export statement.

You can now use this minimal layout for your Studio route. This should resolve the `<noscript>` error when rendering Sanity Studio in your Next.js app.

If you need to move your Studio route into a route group (e.g., `(studio)/studio/`), let me know and I can guide you through that as well!

---

_**User (2025-07-24 14:08Z)**_

Error: A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:

- A server/client branch `if (typeof window !== 'undefined')`.
- Variable input such as `Date.now()` or `Math.random()` which changes each time it's called.
- Date formatting in a user's locale which doesn't match the server.
- External changing data without sending a snapshot of it along with the HTML.
- Invalid HTML tag nesting.

It can also happen if the client has a browser extension installed which messes with the HTML before React loaded.

https://react.dev/link/hydration-mismatch

  ...
    <ColorThemeProvider scheme="system">
      <ThemeProvider scheme="light" theme={{_version:0, ...}}>
        <ot theme={{sanity:{...}}}>
          <ToastProvider paddingY={7} zOffset={[...]}>
            <StudioErrorBoundary>
              <ErrorBoundary onCatch={function StudioErrorBoundary.useCallback[handleCatchError]}>
                <ErrorBoundary onCatch={function ErrorBoundary.useCallback[handleCatch]}>
                  <StudioRootErrorHandler>
                    <WorkspacesProvider config={{basePath:"...", ...}} basePath={undefined} ...>
                      <LoadingBlock>
                        <Styled(ForwardRef(Card)) $fill={undefined} as="div" data-testid="loading-block">
                          <div
                            data-testid="loading-block"
+                           className="sc-hwkwBN iXhJxh"
-                           className="sc-kNOymR glWnYO"
                          >
                            ...
                              <StyledSpinner data-ui="Spinner" muted={true} className="sc-kNOymR ..." ref={null}>
                                <ForwardRef(Text) data-ui="Spinner" muted={true} className="StyledSpin...">
                                  <StyledText data-ui="Spinner" className="StyledSpin..." $accent={false} $align={[...]} ...>
                                    <div
                                      data-ui="Spinner"
+                                     className="StyledText-sc-11ov82j-0 kKPMdH StyledSpinner-sc-124hnd0-0 esQOtu sc-k..."
-                                     className="StyledText-sc-11ov82j-0 kKPMdH StyledSpinner-sc-124hnd0-0 esQOtu sc-d..."
                                    >

    at createConsoleError (http://localhost:3000/_next/static/chunks/79591_next_dist_ac79633a._.js:1484:71)
    at handleConsoleError (http://localhost:3000/_next/static/chunks/79591_next_dist_ac79633a._.js:2090:54)
    at console.error (http://localhost:3000/_next/static/chunks/79591_next_dist_ac79633a._.js:2243:57)
    at http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:3012:25
    at runWithFiberInDEV (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:890:74)
    at emitPendingHydrationWarnings (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:3011:13)
    at completeWork (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:6409:32)
    at runWithFiberInDEV (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:890:74)
    at completeUnitOfWork (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8301:23)
    at performUnitOfWork (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8238:28)
    at workLoopConcurrentByScheduler (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8232:58)
    at renderRootConcurrent (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8214:71)
    at performWorkOnRoot (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:7846:176)
    at performWorkOnRootViaSchedulerTask (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_react-dom_f1ee8a78._.js:8820:9)
    at MessagePort.performWorkUntilDeadline (http://localhost:3000/_next/static/chunks/79591_next_dist_compiled_a9703d93._.js:2588:64)

---

_**Assistant**_

This new error is a **hydration mismatch** between the server-rendered HTML and what React expects on the client. This is common when using Sanity Studio (or other complex React apps) inside Next.js App Router, especially if:

- The Studio is rendered as a Client Component, but the layout or parent is a Server Component.
- There are mismatches in how styles or dynamic values are generated between server and client.
- The Studio is not fully isolated from your main app's context.

### Why this happens with Sanity Studio

Sanity Studio is a **fully client-side React app**. It expects to control its own rendering, theming, and sometimes even its own `<html>` and `<body>` tags. If you render it inside a Next.js App Router route, you must ensure:

- The layout is as minimal as possible (which you did).
- The Studio page is a **Client Component** (`'use client'` at the top).
- No server-only logic or context leaks into the Studio.
- No dynamic values (like `Date.now()`, `Math.random()`, or browser-only APIs) are used in the layout or page unless properly handled.

---

## **How to Fix Hydration Mismatch for Sanity Studio in Next.js App Router**

### 1. **Ensure the Studio Page is a Client Component**

Your `app/studio/[[...tool]]/page.tsx` should start with:

```tsx
'use client';
```

This ensures everything inside is rendered on the client, matching what Sanity Studio expects.

### 2. **Minimal Layout**

Your `app/studio/layout.tsx` is already minimal, which is good.

### 3. **No Server/Client Branching in Studio Layout**

Do **not** use any `if (typeof window !== 'undefined')` or similar logic in the Studio layout or page.

### 4. **No Dynamic Values in Layout**

Do **not** use `Date.now()`, `Math.random()`, or any value that changes between server and client in the layout or Studio page.

### 5. **No Styled Components Registry or Providers in Studio Layout**

If you use styled-components or other providers in your main app, **do not** include them in the Studio layout. Sanity Studio manages its own styles.

---

## **Example: Correct Studio Setup**

**app/studio/layout.tsx**
```tsx
// Minimal layout for Sanity Studio
export default function StudioLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  );
}
```

**app/studio/[[...tool]]/page.tsx**
```tsx
'use client';

import { NextStudio } from 'next-sanity/studio';
import config from '../../../sanity.config';

export default function StudioPage() {
  return <NextStudio config={config} />;
}
```

---

## **If You Still Get Hydration Mismatches**

- **Clear `.next` and restart your dev server**:  
  Sometimes old builds cause mismatches.
  ```
  rm -rf .next
  npm run dev
  ```
- **Check for browser extensions**:  
  Some extensions (like ad blockers or dark mode injectors) can modify the DOM before React hydrates, causing mismatches.
- **Check for custom fonts or CSS-in-JS**:  
  If you use custom fonts or CSS-in-JS in your main app, make sure they are not included in the Studio layout.

---

## **Summary**

- Studio layout: minimal, no dynamic values, no providers.
- Studio page: `'use client'` at the top.
- No server/client branching in Studio files.
- Clear build cache and restart.

---

Would you like me to add `'use client'` to your Studio page, or help you check for any other possible sources of mismatch?

---

_**User (2025-07-24 14:08Z)**_

add use client

---

_**Assistant**_

Tool use: **edit_file**


<details><summary>Edit file: app/studio/[[...tool]]/page.tsx</summary>

Add 'use client' directive at the top of the Studio page to ensure it is rendered as a client component.

**Chunk 1**
Lines added: 2, lines removed: undefined

```diff
@@ -1,2 +1,4 @@
+ 'use client';
+ 
  /**
   * This route is responsible for the built-in authoring environment using Sanity Studio.
```

</details>

---

Tool use: **edit_file**


<details><summary>Edit file: app/studio/[[...tool]]/page.tsx</summary>

Remove the export of 'metadata' and 'viewport' from a client component, as this is not allowed in Next.js. Also, ensure there are no stray characters before the 'use client' directive.

**Chunk 1**
Lines added: undefined, lines removed: 2

```diff
@@ -14,6 +14,4 @@
  
  export const dynamic = 'force-static'
- 
- export { metadata, viewport } from 'next-sanity/studio'
  
  export default function StudioPage() {
```

</details>

---

Tool use: **edit_file**

---

The `'use client'` directive has been added to your Studio page, and the forbidden `metadata`/`viewport` exports have been removed. This ensures the Studio page is a proper client component, which is required for Sanity Studio to work correctly in Next.js App Router.

If you still see a linter error about "Unknown word (CssSyntaxError)" on line 1, make sure:
- There are **no blank lines, spaces, or comments before** `'use client';` at the very top of the file.
- The file should start exactly like this:
  ```tsx
  'use client';

  /**
   * This route is responsible for the built-in authoring environment using Sanity Studio.
   * ...
  ```

You can now test your Studio route. This should resolve both the `<noscript>` error and the hydration mismatch.

If you still encounter issues, let me know the exact error and I’ll help you further!

---

