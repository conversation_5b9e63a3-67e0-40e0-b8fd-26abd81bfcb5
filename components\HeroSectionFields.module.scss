@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50vw;
  height: 93vh;
  border-radius: rem(150) rem(150) rem(150) rem(150);

  @include only(largeDesktop) {
    background-color: #c9e5e8;
  }
  @include only(smallDesktop) {
    width: 95vw !important;
    height: 93vh;
    background-color: #c9e5e8;
  }

  @include only(tablet) {
    width: 95vw;
    height: 112vh;
    background-color: #c9e5e8;
  }

  .formContainer {
    width: 100%;
    height: 100%;

    @include only(smallDesktop) {
      display: flex;
      justify-content: flex-end;
    }

    .formWrapper {
      display: flex;
      flex-direction: column;
      margin-top: rem(120);
      align-items: center;

      @include only(largeDesktop) {
      }
      @include only(smallDesktop) {
        margin-right: rem(144);
      }
    }

    .logo {
      margin-top: rem(48);
      margin-bottom: rem(48);

      @include only(largeDesktop) {
        width: rem(120);
        height: "auto";
      }
      @include only(smallDesktop) {
        width: rem(120);
        height: "auto";
      }
      @include only(tablet) {
        position: relative;
        top: rem(-330);
        width: rem(100);
        height: "auto";
      }
      @include only(extraLargeDesktop) {
        width: rem(120);
        height: "auto";
      }
    }
    .buttonsAndSearch {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      @include only(largeDesktop) {
      }
      @include only(smallDesktop) {
      }
      @include only(tablet) {
        margin-top: rem(230);
      }

      .buttons {
        display: flex;
        flex-direction: column;
        margin-bottom: rem(64);

        .link {
        }

        .createAListingBtn {
          margin-bottom: rem(20);
          background-color: $danger-hover;
          color: $white-one;
        }

        .createAListingBtn:hover {
          background-color: #f83710;
        }

        .createAListingBtn:focus {
          outline-offset: rem(1);
          outline-style: solid;
          outline-color: $danger-hover;
          outline-width: rem(2);
          border-radius: rem(40);
        }

        .categoriesBtn {
          background-color: $white-two;
          box-shadow: none;
        }

        .categoriesBtn:hover {
          background-color: $white-one;
        }
      }
    }
  }
}
