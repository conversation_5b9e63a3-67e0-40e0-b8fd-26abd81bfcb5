{"name": "oly", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbo", "lint": "eslint .", "serve": "node server.js", "extract-variables": "node ./extract-variables.js", "build": "npm run extract-variables && next build", "sync-articles": "node --loader ts-node/esm scripts/sync-articles.ts", "sync-articles-cron": "node sync-articles-working.cjs", "sync-articles-now": "node sync-articles-working.cjs", "typegen": "sanity schema extract --path=./sanity/extract.json && sanity typegen generate"}, "dependencies": {"@adobe/css-tools": "^4.4.4", "@csstools/normalize.css": "^12.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.2.1", "@hookform/resolvers": "^3.3.4", "@imagekit/next": "^2.1.3", "@mux/mux-player": "^3.5.3", "@phosphor-icons/react": "^2.1.7", "@popperjs/core": "^2.11.8", "@portabletext/react": "^4.0.3", "@preact/signals-react": "^3.3.1", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-toast": "^1.1.5", "@react-google-maps/api": "^2.19.3", "@sanity/client": "^7.11.2", "@sanity/icons": "^3.7.4", "@sanity/image-url": "^1.2.0", "@sanity/structure": "^2.36.2", "@sanity/types": "^4.6.0", "@sanity/ui": "^3.1.4", "@sanity/vision": "^4.9.0", "@shadcn/ui": "^0.0.4", "@syncfusion/ej2-react-buttons": "^23.2.4", "@tanstack/react-query": "^5.90.2", "@tanstack/react-query-devtools": "^5.90.2", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-focus": "^2.4.0", "@tiptap/extension-placeholder": "^2.4.0", "@tiptap/extension-underline": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@trpc/client": "^11.0.0-next-beta.264", "@trpc/next": "^11.0.0-next-beta.264", "@trpc/react-query": "^11.0.0-next-beta.264", "@trpc/server": "^11.0.0-next-beta.264", "@types/express": "^4.17.21", "@types/youtube": "^0.0.50", "@uploadthing/react": "^7.3.3", "@workos-inc/authkit-nextjs": "^2.10.0", "@workos-inc/node": "^7.72.0", "@xata.io/client": "^0.28.0", "axios": "^1.6.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.0", "cron": "^4.3.3", "date-fns": "^3.6.0", "dotenv": "^16.4.1", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.14.2", "eslint": "^9.17.0", "eslint-config-next": "16.0.0", "express": "^4.18.2", "font-awesome": "^4.7.0", "framer-motion": "^12.4.7", "froala-editor": "^4.2.1", "gsap": "^3.12.7", "image-size": "^1.1.1", "imagekit": "^6.0.0", "lodash": "^4.17.21", "lucide-react": "^0.548.0", "macy": "^2.5.1", "masonry-layout": "^4.2.2", "media-chrome": "^4.5.0", "meilisearch": "^0.36.0", "newsdataapi": "^1.0.3", "next": "16.0.0", "next-sanity": "^11.1.1", "next-superjson-plugin": "^0.6.2", "next-themes": "^0.2.1", "next-video": "^2.0.4", "nodemailer": "^6.9.9", "normalize.css": "^8.0.1", "only": "^0.0.2", "payload": "^3.48.0", "player.style": "^0.1.8", "qrcode.react": "^4.2.0", "radix-ui": "^1.4.3", "react": "^19.2.0", "react-chat-engine": "^0.1.0", "react-chat-engine-advanced": "^0.1.28", "react-datepicker": "^7.3.0", "react-day-picker": "^9.11.1", "react-dom": "^19.2.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.5", "react-fast-marquee": "^1.6.5", "react-froala-wysiwyg": "^4.2.1", "react-google-places-autocomplete": "^4.1.0", "react-hook-form": "^7.52.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.13.0", "react-masonry-css": "^1.0.16", "react-photo-gallery": "^8.0.0", "react-responsive": "^9.0.2", "react-responsive-masonry": "^2.1.7", "react-select": "^5.8.0", "react-slick": "^0.31.0", "react-smooth-corners": "^1.0.4", "react-webcam": "^7.2.0", "react-youtube": "^10.1.0", "sanity": "^4.9.0", "sanity-plugin-asset-source-unsplash": "^4.0.1", "scrollmagic": "^2.0.8", "server": "^1.0.42", "server-only": "^0.0.1", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "smooth-corners": "^1.0.8", "sonner": "^1.4.0", "stripe": "^14.19.0", "styled-components": "^6.1.19", "svix": "^1.75.0", "swiper": "^11.1.3", "typesense": "^1.7.2", "undici": "^7.16.0", "unsplash-js": "^7.0.19", "uploadthing": "^6.10.1", "usehooks-ts": "^3.1.0", "uuid": "^10.0.0", "zod": "^3.23.8", "zod-validation-error": "^3.3.0", "zustand": "^4.4.7"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/imagekit": "^3.1.5", "@types/node": "^20.12.7", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/react": "19.2.2", "@types/react-dom": "19.2.2", "@types/react-query": "^1.2.8", "@types/react-slick": "^0.23.12", "@types/scrollmagic": "^2.0.8", "@types/styled-components": "^5.1.34", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "jest": "^30.0.4", "node-loader": "^2.0.0", "nodemon": "^3.0.3", "postcss": "^8.4.33", "postcss-loader": "^7.3.4", "sass": "^1.93.2", "sass-extract": "^3.0.0", "sass-loader": "^13.3.0", "scss": "^0.2.4", "style-loader": "^4.0.0", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "optionalDependencies": {"@img/sharp-win32-x64": "^0.33.2"}, "overrides": {"@types/react": "19.2.2", "@types/react-dom": "19.2.2", "next": "$next", "axios": "^1.6.2", "glob": "^10.0.0", "inflight": "npm:@isaacs/inflight@^1.0.6"}, "resolutions": {"sass": "^1.69.5"}}