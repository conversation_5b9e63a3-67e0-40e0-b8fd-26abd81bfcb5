export const mileageData = [
  {
    id: 1,
    label: "Very Low Mileage",
    range: "0 – 30,000 km",
    value: "Below 30,000 km",
    description: "Almost new, demo or ex-company car",
    typicalAge: "1–2 years old",
  },
  {
    id: 2,
    label: "Low Mileage",
    range: "30,000 – 60,000 km",
    value: "Below 60,000 km",
    description: "Lightly used, often still under motor plan",
    typicalAge: "2–4 years old",
  },
  {
    id: 3,
    label: "Moderate Mileage",
    range: "60,000 – 100,000 km",
    value: "Below 100,000 km",
    description: "Average usage for its age, good condition expected",
    typicalAge: "3–6 years old",
  },
  {
    id: 4,
    label: "High Mileage",
    range: "100,000 – 150,000 km",
    value: "Below 150,000 km",
    description: "Out of warranty, expect normal wear and tear",
    typicalAge: "5–8 years old",
  },
  {
    id: 5,
    label: "Very High Mileage",
    range: "150,000 – 200,000 km",
    value: "Below 200,000 km",
    description: "Well-used, priced lower, may need minor repairs",
    typicalAge: "8–10 years old",
  },
  {
    id: 6,
    label: "Ultra High Mileage",
    range: "200,000+ km",
    value: "Above 200,000 km",
    description: "Older vehicles or workhorses like bakkies and taxis",
    typicalAge: "10+ years old",
  },
];
