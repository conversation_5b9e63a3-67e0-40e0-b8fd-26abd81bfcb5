<!-- Generated by SpecStory -->

<!-- cursor Session 685df8b8-83e2-4227-99e3-f459b9f6f6ff (2025-07-21 19:06Z) -->

# Fixing unique key prop error in React (2025-07-21 19:06Z)

_**User (2025-07-22 09:28Z)**_

@page.tsx  I am getting this error: Error: Each child in a list should have a unique "key" prop.

Check the render method of `CategoriesClient`. See https://react.dev/link/warning-keys for more information.
    at createConsoleError (http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_48ee97d5._.js:473:71)
    at handleConsoleError (http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_48ee97d5._.js:627:54)
    at console.error (http://localhost:3000/_next/static/chunks/node_modules_next_dist_client_48ee97d5._.js:773:57)
    at http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:15205:29
    at runWithFiberInDEV (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3081:74)
    at warnForMissingKey (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:15204:17)
    at warnOnInvalidKey (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:5730:21)
    at reconcileChildrenArray (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:5768:160)
    at reconcileChildFibersImpl (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:5874:117)
    at http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:5901:39
    at reconcileChildren (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:7485:51)
    at beginWork (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:8270:1573)
    at runWithFiberInDEV (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:3081:74)
    at performUnitOfWork (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:10404:97)
    at workLoopSync (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:10296:40)
    at renderRootSync (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:10279:13)
    at performWorkOnRoot (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:10014:212)
    at performSyncWorkOnRoot (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:10996:9)
    at flushSyncWorkAcrossRoots_impl (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:10916:316)
    at processRootScheduleInMicrotask (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:10936:106)
    at http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:11007:158

---

_**Assistant**_



---

