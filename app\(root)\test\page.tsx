"use client";
import Modal from "@/components/Modal";
import RangeSlider from "@/components/RangeSlider";
import SetVehiclePrice from "@/components/SetVehiclePrice";
import VehicleBodyType from "@/components/VehicleBodyType";
import VehicleMake from "@/components/VehicleMake";
import VehiclePrice from "@/components/VehiclePrice";
import VehicleTrim from "@/components/VehicleTrim";
import { useState } from "react";

const Page = () => {
  const [showModal, setShowModal] = useState(false);
  const [price, setPrice] = useState(0);
  return (
    <div style={{ marginTop: "6rem" }}>
      <Modal
        showModal={true}
        setShowModal={() => setShowModal(false)}
        // modalContent={<VehicleMake />}
        // modalContent={<VehicleBodyType  />}
        // modalContent={<VehicleTrim  />}
        // modalContent={<VehiclePrice  />}
        // modalContent={<SetVehiclePrice  />}
        modalContent={
          <RangeSlider
            placeholder="Set Maximum Price"
            maxPrice={250_000}
            step={1_000}
            onValueChange={setPrice}
          />
        }
      />
    </div>
  );
};

export default Page;
