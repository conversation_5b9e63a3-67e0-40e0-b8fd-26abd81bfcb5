@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  position: static !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: rem(740);
  height: auto !important;
  margin-top: rem(288) !important;
  margin-bottom: rem(144) !important;
  background-color: $white-four;

  .title {
    position: static !important;
    margin-top: rem(288);
    // margin-bottom: rem(48);
    font-size: $h4;
    font-weight: 500;
    color: $danger;
  }
  .videoContainer {
    position: static !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: rem(72);
    .video {
      margin-bottom: rem(20);
    }
    .thumbnailsContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: rem(48);
      .thumbnailTitle {
        margin-bottom: rem(20);
      }

      .thumbnails {
        display: flex;
        gap: rem(8);
        .thumbnail {
          overflow: hidden;
          width: rem(208);
          height: rem(117);
          border-radius: rem(40);
          background-color: $grey-one;
          cursor: pointer;
        }

        .customThumbnail {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: $white-two;
        }
      }
    }
  }

  .detailsContainer {
    position: static !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: rem(72);
    .control {
      margin-bottom: rem(20);
    }

    .videoTitleContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      .errorMessage {
        margin-bottom: rem(8);
        color: $danger;
      }
      .videoTitle {
        width: rem(640);
      }
    }

    .descriptionContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: rem(48);
      .errorMessage {
        margin-bottom: rem(8);
        color: $danger;
      }
      .description {
        width: rem(640);
      }
    }
  }

  .publishButtonContainer {
  }
}
