@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.wrapper {
  width: rem(360);
  height: 100%;
  @include only(largeDesktop) {
    width: 80vw;
  }
  @include only(smallDesktop) {
    width: 96vw;
  }
  @include only(tablet) {
    width: 96vw;
  }

  .keepSidebarInPlace {
    @include only(largeDesktop) {
      width: rem(342);
      width: rem(342);
    }
    @include only(smallDesktop) {
      width: rem(342);
      width: rem(342);
    }
    @include only(tablet) {
      width: rem(342);
      width: rem(342);
    }
  }
  .sidebar {
    position: fixed;
    top: rem(340);
    transform: translate(0, -50%);

    @include only(largeDesktop) {
      display: unset;
      top: 50%;
    }
    @include only(smallDesktop) {
      display: unset;
      top: 50%;
    }
    @include only(tablet) {
      display: unset;
      top: 50%;
    }
  }

  .main {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: rem(100);
    @include only(largeDesktop) {
      width: rem(1098);
    }
    @include only(smallDesktop) {
      width: rem(1098) !important;
      // margin-left: rem(16);
    }
    @include only(tablet) {
      width: rem(1098);
      // margin-left: rem(16);
    }
  }
}
