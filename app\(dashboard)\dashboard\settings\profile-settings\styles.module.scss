@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  .title {
    font-size: $h4;
    font-weight: 500;
    color: $danger;
    margin-top: rem(80);
    margin-bottom: rem(52);
  }

  .avatarContainer {
    margin-bottom: rem(24);
    cursor: url("/icons/pencil.png"), auto;
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: rem(48);
    margin-bottom: rem(48);

    .errorMessage {
      color: $danger !important;
      margin-bottom: rem(8);
    }
    .controls {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .control {
        margin-bottom: rem(20);
      }

      .phoneNumber {
        // margin-bottom: rem(48);
      }

      .changePasswordButton {
        color: #67787c;
        margin-bottom: rem(48);
      }
      .selectContainer {
        margin-bottom: rem(20);
      }
    }

    .socialMediaLinks {
      .otherSocialMedia {
        //
      }
      .selectedSocialMediaContainer {
        display: flex;
        flex-direction: column;
        align-items: center;

        .link {
          margin-bottom: rem(48);
        }
      }
      .otherSocialMediaContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: rem(48);
      }
    }
  }

  .buttonsContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: rem(240);

    .updateProfileSettings {
      margin-bottom: rem(20);
    }

    .backButton {
    }
  }
}
