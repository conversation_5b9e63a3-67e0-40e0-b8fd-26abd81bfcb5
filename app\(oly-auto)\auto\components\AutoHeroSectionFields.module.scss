@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50vw;
  height: 93vh;
  border-radius: rem(150) rem(150) rem(150) rem(150);
  background-color: #c9dce8;

  @include only(largeDesktop) {
  }
  @include only(smallDesktop) {
    width: 95vw !important;
    height: 93vh;
  }

  @include only(tablet) {
    width: 95vw;
    height: 112vh;
  }

  .formContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    width: 100%;
    height: 100%;

    @include only(smallDesktop) {
      display: flex;
      justify-content: flex-end;
    }

    .formWrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      // margin-top: rem(120);
      align-items: center;
      @include only(largeDesktop) {
      }
      @include only(smallDesktop) {
        margin-right: rem(144);
      }
    }

    .logo {
      // margin-top: rem(48);
      margin-bottom: rem(20);

      @include only(largeDesktop) {
        width: rem(120);
        height: "auto";
      }
      @include only(smallDesktop) {
        width: rem(120);
        height: "auto";
      }
      @include only(tablet) {
        position: relative;
        top: rem(-330);
        width: rem(100);
        height: "auto";
      }
      @include only(extraLargeDesktop) {
        width: rem(120);
        height: "auto";
      }
    }
    .buttonsAndSearch {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      @include only(largeDesktop) {
      }
      @include only(smallDesktop) {
      }
      @include only(tablet) {
        margin-top: rem(230);
      }
    }
  }
}
