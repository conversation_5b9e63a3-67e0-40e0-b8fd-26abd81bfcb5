@use "@/utils/functions" as *;
@use "@/utils/variables" as *;
@use "@/utils/breakpoints" as *;

.container {
  position: relative;
  display: flex;
  background-color: $white-three;
  width: rem(918);
  height: rem(378);
  border-radius: rem(40);
  cursor: pointer;

  .checkboxContainer {
    position: absolute;
    top: rem(20);
    right: rem(20);
  }

  .content {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
  }

  .imageContainer {
    position: relative;
    width: 60%;
    height: 100%;
    border-radius: rem(40);
    box-shadow: $shadow-one;
  }

  .details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: rem(20);
    width: 40%;
    height: 100%;
    border-radius: rem(40);

    .bodyType {
      font-size: $h4;
      font-weight: 500;
    }

    .buttonContainer {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: rem(20);
      .button {
      position: relative;
   
        background-color: $white-one;
        width: rem(280);

        .buttonChildren {
          font-size: rem(14);
          .count {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: rem(0);
          font-size: rem(12);
          // font-weight: 600;
          margin-right: rem(12);
          padding: rem(4) rem(8);
          border-radius: rem(20);
          background-color: $grey-one;
          color: $black-one;
        }
        }
      }

      
      
    }
  }
}
